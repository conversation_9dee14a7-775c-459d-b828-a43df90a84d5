package sampler

import (
	"time"

	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/shirou/gopsutil/v3/disk"
	"github.com/shirou/gopsutil/v3/net"
)

// Sample represents a point-in-time sample of system metrics
type Sample struct {
	Timestamp time.Time
	CPU       []cpu.TimesStat
	Disk      map[string]disk.IOCountersStat
	Network   []net.IOCountersStat
}

// Sampler handles collecting and calculating rate-based metrics
type Sampler struct {
	interval time.Duration
}

// NewSampler creates a new sampler with the specified interval
func NewSampler(interval time.Duration) *Sampler {
	return &Sampler{
		interval: interval,
	}
}

// TakeSample collects a snapshot of current system metrics
func (s *Sampler) TakeSample() (*Sample, error) {
	sample := &Sample{
		Timestamp: time.Now(),
	}

	// CPU times
	cpuTimes, err := cpu.Times(true)
	if err != nil {
		return nil, err
	}
	sample.CPU = cpuTimes

	// Disk I/O
	diskIO, err := disk.IOCounters()
	if err != nil {
		return nil, err
	}
	sample.Disk = diskIO

	// Network I/O
	netIO, err := net.IOCounters(true)
	if err != nil {
		return nil, err
	}
	sample.Network = netIO

	return sample, nil
}

// CPURate calculates CPU usage rates between two samples
type CPURate struct {
	CPU       string
	User      float64
	System    float64
	Idle      float64
	IOWait    float64
	IRQ       float64
	SoftIRQ   float64
	Steal     float64
	Guest     float64
	GuestNice float64
}

// CalculateCPURate calculates CPU usage rates between two samples
func CalculateCPURate(before, after *Sample) []CPURate {
	if len(before.CPU) != len(after.CPU) {
		return nil
	}

	duration := after.Timestamp.Sub(before.Timestamp).Seconds()
	if duration <= 0 {
		return nil
	}

	var rates []CPURate
	for i := 0; i < len(before.CPU); i++ {
		b := before.CPU[i]
		a := after.CPU[i]

		totalBefore := b.User + b.System + b.Idle + b.Iowait + b.Irq + b.Softirq + b.Steal + b.Guest + b.GuestNice
		totalAfter := a.User + a.System + a.Idle + a.Iowait + a.Irq + a.Softirq + a.Steal + a.Guest + a.GuestNice
		totalDiff := totalAfter - totalBefore

		if totalDiff <= 0 {
			continue
		}

		rate := CPURate{
			CPU:       a.CPU,
			User:      ((a.User - b.User) / totalDiff) * 100,
			System:    ((a.System - b.System) / totalDiff) * 100,
			Idle:      ((a.Idle - b.Idle) / totalDiff) * 100,
			IOWait:    ((a.Iowait - b.Iowait) / totalDiff) * 100,
			IRQ:       ((a.Irq - b.Irq) / totalDiff) * 100,
			SoftIRQ:   ((a.Softirq - b.Softirq) / totalDiff) * 100,
			Steal:     ((a.Steal - b.Steal) / totalDiff) * 100,
			Guest:     ((a.Guest - b.Guest) / totalDiff) * 100,
			GuestNice: ((a.GuestNice - b.GuestNice) / totalDiff) * 100,
		}
		rates = append(rates, rate)
	}

	return rates
}

// DiskRate represents disk I/O rates
type DiskRate struct {
	Device      string
	ReadPS      float64  // reads per second
	WritePS     float64  // writes per second
	ReadBPS     float64  // bytes read per second
	WriteBPS    float64  // bytes written per second
	AvgQueueLen float64  // average queue length
	AwaitMS     float64  // average wait time in milliseconds
	Util        float64  // utilization percentage
}

// CalculateDiskRate calculates disk I/O rates between two samples
func CalculateDiskRate(before, after *Sample) []DiskRate {
	duration := after.Timestamp.Sub(before.Timestamp).Seconds()
	if duration <= 0 {
		return nil
	}

	var rates []DiskRate
	for device, afterStat := range after.Disk {
		beforeStat, exists := before.Disk[device]
		if !exists {
			continue
		}

		readDiff := float64(afterStat.ReadCount - beforeStat.ReadCount)
		writeDiff := float64(afterStat.WriteCount - beforeStat.WriteCount)
		readBytesDiff := float64(afterStat.ReadBytes - beforeStat.ReadBytes)
		writeBytesDiff := float64(afterStat.WriteBytes - beforeStat.WriteBytes)
		readTimeDiff := float64(afterStat.ReadTime - beforeStat.ReadTime)
		writeTimeDiff := float64(afterStat.WriteTime - beforeStat.WriteTime)
		ioTimeDiff := float64(afterStat.IoTime - beforeStat.IoTime)

		rate := DiskRate{
			Device:   device,
			ReadPS:   readDiff / duration,
			WritePS:  writeDiff / duration,
			ReadBPS:  readBytesDiff / duration,
			WriteBPS: writeBytesDiff / duration,
		}

		// Calculate average wait time
		totalOps := readDiff + writeDiff
		if totalOps > 0 {
			rate.AwaitMS = (readTimeDiff + writeTimeDiff) / totalOps
		}

		// Calculate utilization (percentage of time device was busy)
		rate.Util = (ioTimeDiff / (duration * 1000)) * 100
		if rate.Util > 100 {
			rate.Util = 100
		}

		rates = append(rates, rate)
	}

	return rates
}

// NetworkRate represents network interface rates
type NetworkRate struct {
	Interface string
	RxBPS     float64 // bytes received per second
	TxBPS     float64 // bytes transmitted per second
	RxPPS     float64 // packets received per second
	TxPPS     float64 // packets transmitted per second
	RxErrPS   float64 // receive errors per second
	TxErrPS   float64 // transmit errors per second
	RxDropPS  float64 // receive drops per second
	TxDropPS  float64 // transmit drops per second
}

// CalculateNetworkRate calculates network interface rates between two samples
func CalculateNetworkRate(before, after *Sample) []NetworkRate {
	duration := after.Timestamp.Sub(before.Timestamp).Seconds()
	if duration <= 0 {
		return nil
	}

	var rates []NetworkRate
	
	// Create a map for easier lookup
	beforeMap := make(map[string]net.IOCountersStat)
	for _, stat := range before.Network {
		beforeMap[stat.Name] = stat
	}

	for _, afterStat := range after.Network {
		beforeStat, exists := beforeMap[afterStat.Name]
		if !exists {
			continue
		}

		rate := NetworkRate{
			Interface: afterStat.Name,
			RxBPS:     float64(afterStat.BytesRecv-beforeStat.BytesRecv) / duration,
			TxBPS:     float64(afterStat.BytesSent-beforeStat.BytesSent) / duration,
			RxPPS:     float64(afterStat.PacketsRecv-beforeStat.PacketsRecv) / duration,
			TxPPS:     float64(afterStat.PacketsSent-beforeStat.PacketsSent) / duration,
			RxErrPS:   float64(afterStat.Errin-beforeStat.Errin) / duration,
			TxErrPS:   float64(afterStat.Errout-beforeStat.Errout) / duration,
			RxDropPS:  float64(afterStat.Dropin-beforeStat.Dropin) / duration,
			TxDropPS:  float64(afterStat.Dropout-beforeStat.Dropout) / duration,
		}

		rates = append(rates, rate)
	}

	return rates
}
