openapi: 3.0.3
info:
  title: Lil Box API
  description: API documentation for Lil Box application
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>

servers:
  - url: http://localhost:8080
    description: Development server

paths:
  /:
    get:
      summary: Root endpoint
      description: Returns a welcome message
      responses:
        '200':
          description: Successful response
          content:
            text/plain:
              schema:
                type: string
                example: "Hello from lil_box server!"

  /health:
    get:
      summary: Health check endpoint
      description: Returns the health status of the application
      responses:
        '200':
          description: Application is healthy
          content:
            text/plain:
              schema:
                type: string
                example: "OK"

components:
  schemas:
    Error:
      type: object
      properties:
        error:
          type: string
          description: Error message
        code:
          type: integer
          description: Error code
      required:
        - error
        - code
