#!/bin/bash

# Build script for lil_box

set -e

echo "Building lil_box..."

# Clean previous builds
echo "Cleaning previous builds..."
rm -rf bin/

# Create bin directory
mkdir -p bin/

# Build for current platform
echo "Building for current platform..."
go build -o bin/lil_box ./cmd/server

# Build for Linux (if not already on Linux)
if [[ "$OSTYPE" != "linux-gnu"* ]]; then
    echo "Cross-compiling for Linux..."
    CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o bin/lil_box_linux ./cmd/server
fi

# Build for Windows (if not already on Windows)
if [[ "$OSTYPE" != "msys" ]] && [[ "$OSTYPE" != "cygwin" ]]; then
    echo "Cross-compiling for Windows..."
    CGO_ENABLED=0 GOOS=windows GOARCH=amd64 go build -o bin/lil_box.exe ./cmd/server
fi

echo "Build completed successfully!"
echo "Binaries are available in the bin/ directory"
