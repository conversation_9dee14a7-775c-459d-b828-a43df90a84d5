package sysinfo

import (
	"encoding/json"
	"fmt"

	"lil_box/pkg/sysinfo"

	"github.com/spf13/cobra"
)

var cpuCmd = &cobra.Command{
	Use:   "cpu",
	Short: "查看CPU使用情况",
	Long:  "显示CPU使用情况，包括CPU型号、核心数、使用率和系统负载信息",
	Aliases: []string{"c"},
	Run: func(cmd *cobra.Command, args []string) {
		jsonOutput, _ := cmd.Flags().GetBool("json")
		
		if jsonOutput {
			info, err := sysinfo.GetCPUInfo()
			if err != nil {
				fmt.Printf("Error: %v\n", err)
				return
			}
			
			jsonData, err := json.MarshalIndent(info, "", "  ")
			if err != nil {
				fmt.Printf("Error formatting JSON: %v\n", err)
				return
			}
			fmt.Println(string(jsonData))
		} else {
			if err := sysinfo.PrintCPUInfo(); err != nil {
				fmt.Printf("Error: %v\n", err)
			}
		}
	},
}

func init() {
	rootCmd.AddCommand(cpuCmd)
}
