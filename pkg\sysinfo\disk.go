package sysinfo

import (
	"fmt"
	"lil_box/pkg/utils"

	"github.com/shirou/gopsutil/v3/disk"
)

// DiskInfo represents disk information
type DiskInfo struct {
	Device      string  `json:"device"`
	Mountpoint  string  `json:"mountpoint"`
	Fstype      string  `json:"fstype"`
	Total       uint64  `json:"total"`
	Free        uint64  `json:"free"`
	Used        uint64  `json:"used"`
	UsedPercent float64 `json:"used_percent"`
}

// DiskIOInfo represents disk I/O information
type DiskIOInfo struct {
	Name         string `json:"name"`
	ReadCount    uint64 `json:"read_count"`
	WriteCount   uint64 `json:"write_count"`
	ReadBytes    uint64 `json:"read_bytes"`
	WriteBytes   uint64 `json:"write_bytes"`
	ReadTime     uint64 `json:"read_time"`
	WriteTime    uint64 `json:"write_time"`
	IoTime       uint64 `json:"io_time"`
	WeightedIO   uint64 `json:"weighted_io"`
}

// GetDiskInfo retrieves disk usage information
func GetDiskInfo() ([]DiskInfo, error) {
	partitions, err := disk.Partitions(false)
	if err != nil {
		return nil, fmt.Errorf("获取磁盘分区信息失败: %v", err)
	}

	var diskInfos []DiskInfo
	for _, partition := range partitions {
		usage, err := disk.Usage(partition.Mountpoint)
		if err != nil {
			continue // 跳过无法访问的分区
		}

		diskInfo := DiskInfo{
			Device:      partition.Device,
			Mountpoint:  partition.Mountpoint,
			Fstype:      partition.Fstype,
			Total:       usage.Total,
			Free:        usage.Free,
			Used:        usage.Used,
			UsedPercent: usage.UsedPercent,
		}
		diskInfos = append(diskInfos, diskInfo)
	}

	return diskInfos, nil
}

// GetDiskIOInfo retrieves disk I/O information
func GetDiskIOInfo() ([]DiskIOInfo, error) {
	ioCounters, err := disk.IOCounters()
	if err != nil {
		return nil, fmt.Errorf("获取磁盘I/O信息失败: %v", err)
	}

	var ioInfos []DiskIOInfo
	for name, counter := range ioCounters {
		ioInfo := DiskIOInfo{
			Name:         name,
			ReadCount:    counter.ReadCount,
			WriteCount:   counter.WriteCount,
			ReadBytes:    counter.ReadBytes,
			WriteBytes:   counter.WriteBytes,
			ReadTime:     counter.ReadTime,
			WriteTime:    counter.WriteTime,
			IoTime:       counter.IoTime,
			WeightedIO:   counter.WeightedIO,
		}
		ioInfos = append(ioInfos, ioInfo)
	}

	return ioInfos, nil
}

// PrintDiskInfo prints formatted disk information
func PrintDiskInfo() error {
	diskInfos, err := GetDiskInfo()
	if err != nil {
		return err
	}

	fmt.Println("========== 磁盘使用信息 ==========")
	for _, info := range diskInfos {
		fmt.Printf("设备: %s\n", info.Device)
		fmt.Printf("挂载点: %s\n", info.Mountpoint)
		fmt.Printf("文件系统: %s\n", info.Fstype)
		fmt.Printf("总容量: %s\n", utils.FormatBytes(info.Total))
		fmt.Printf("已使用: %s\n", utils.FormatBytes(info.Used))
		fmt.Printf("可用空间: %s\n", utils.FormatBytes(info.Free))
		fmt.Printf("使用率: %.2f%%\n", info.UsedPercent)
		fmt.Println("-----------------------------------------")
	}

	return nil
}

// PrintDiskIOInfo prints formatted disk I/O information
func PrintDiskIOInfo() error {
	ioInfos, err := GetDiskIOInfo()
	if err != nil {
		return err
	}

	fmt.Println("========== 磁盘I/O信息 ==========")
	for _, info := range ioInfos {
		fmt.Printf("设备: %s\n", info.Name)
		fmt.Printf("读取次数: %d\n", info.ReadCount)
		fmt.Printf("写入次数: %d\n", info.WriteCount)
		fmt.Printf("读取字节: %s\n", utils.FormatBytes(info.ReadBytes))
		fmt.Printf("写入字节: %s\n", utils.FormatBytes(info.WriteBytes))
		fmt.Printf("读取时间: %s\n", utils.FormatDuration(info.ReadTime/1000)) // 转换为秒
		fmt.Printf("写入时间: %s\n", utils.FormatDuration(info.WriteTime/1000)) // 转换为秒
		fmt.Println("-----------------------------------------")
	}

	return nil
}
