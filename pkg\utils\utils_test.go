package utils

import (
	"testing"
	"time"
)

func TestGenerateID(t *testing.T) {
	id, err := GenerateID(16)
	if err != nil {
		t.Fatalf("GenerateID failed: %v", err)
	}
	if len(id) != 16 {
		t.<PERSON><PERSON><PERSON>("Expected ID length 16, got %d", len(id))
	}
}

func TestFormatTime(t *testing.T) {
	now := time.Now()
	formatted := FormatTime(now)
	
	parsed, err := time.Parse(time.RFC3339, formatted)
	if err != nil {
		t.Fatalf("Failed to parse formatted time: %v", err)
	}
	
	if !parsed.Equal(now.Truncate(time.Second)) {
		t.<PERSON>("Time formatting/parsing mismatch")
	}
}

func TestToSnakeCase(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"CamelCase", "camel_case"},
		{"XMLHttpRequest", "x_m_l_http_request"},
		{"simpleword", "simpleword"},
		{"", ""},
	}

	for _, test := range tests {
		result := ToSnakeCase(test.input)
		if result != test.expected {
			t.<PERSON><PERSON><PERSON>("ToSnakeCase(%q) = %q, expected %q", test.input, result, test.expected)
		}
	}
}
