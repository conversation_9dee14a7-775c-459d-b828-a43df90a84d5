package sysinfo

import (
	"fmt"
	"runtime"
	"time"

	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/shirou/gopsutil/v3/load"
)

// CPUInfo represents CPU information
type CPUInfo struct {
	ModelName    string    `json:"model_name"`
	Cores        int32     `json:"cores"`
	LogicalCores int       `json:"logical_cores"`
	Usage        []float64 `json:"usage"`
	AvgUsage     float64   `json:"avg_usage"`
	LoadAvg1     float64   `json:"load_avg_1"`
	LoadAvg5     float64   `json:"load_avg_5"`
	LoadAvg15    float64   `json:"load_avg_15"`
}

// GetCPUInfo retrieves CPU information
func GetCPUInfo() (*CPUInfo, error) {
	// 获取CPU基本信息
	cpuInfos, err := cpu.Info()
	if err != nil {
		return nil, fmt.Errorf("获取CPU信息失败: %v", err)
	}

	if len(cpuInfos) == 0 {
		return nil, fmt.Errorf("未找到CPU信息")
	}

	// 获取CPU使用率
	percentages, err := cpu.Percent(time.Second, true)
	if err != nil {
		return nil, fmt.Errorf("获取CPU使用率失败: %v", err)
	}

	// 计算平均使用率
	var totalUsage float64
	for _, p := range percentages {
		totalUsage += p
	}
	avgUsage := totalUsage / float64(len(percentages))

	info := &CPUInfo{
		ModelName:    cpuInfos[0].ModelName,
		Cores:        cpuInfos[0].Cores,
		LogicalCores: runtime.NumCPU(),
		Usage:        percentages,
		AvgUsage:     avgUsage,
	}

	// 获取负载信息 (仅在Linux/Unix系统上可用)
	if runtime.GOOS != "windows" {
		loadInfo, err := load.Avg()
		if err == nil {
			info.LoadAvg1 = loadInfo.Load1
			info.LoadAvg5 = loadInfo.Load5
			info.LoadAvg15 = loadInfo.Load15
		}
	}

	return info, nil
}

// PrintCPUInfo prints formatted CPU information
func PrintCPUInfo() error {
	info, err := GetCPUInfo()
	if err != nil {
		return err
	}

	fmt.Println("========== CPU统计信息 ==========")
	fmt.Printf("CPU型号:              %s\n", info.ModelName)
	fmt.Printf("物理核心数:           %d\n", info.Cores)
	fmt.Printf("逻辑核心数:           %d\n", info.LogicalCores)
	fmt.Printf("平均使用率:           %.2f%%\n", info.AvgUsage)
	fmt.Println("-----------------------------------------")

	// 显示每个核心的使用率
	fmt.Println("各核心使用率:")
	for i, usage := range info.Usage {
		fmt.Printf("  核心 %2d: %6.2f%%\n", i, usage)
	}

	// 显示负载信息 (仅在非Windows系统上)
	if runtime.GOOS != "windows" {
		fmt.Println("-----------------------------------------")
		fmt.Printf("系统负载 (1分钟):     %.2f\n", info.LoadAvg1)
		fmt.Printf("系统负载 (5分钟):     %.2f\n", info.LoadAvg5)
		fmt.Printf("系统负载 (15分钟):    %.2f\n", info.LoadAvg15)
	}

	return nil
}
