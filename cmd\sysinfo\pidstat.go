package sysinfo

import (
	"encoding/json"
	"fmt"
	"lil_box/pkg/commands"
	"strconv"
	"time"

	"github.com/spf13/cobra"
)

var pidstatCmd = &cobra.Command{
	Use:   "pidstat [interval] [count]",
	Short: "显示进程统计信息",
	Long:  "显示进程统计信息，类似于Linux的pidstat命令",
	Args:  cobra.MaximumNArgs(2),
	Run: func(cmd *cobra.Command, args []string) {
		jsonOutput, _ := cmd.Flags().GetBool("json")
		table, _ := cmd.Flags().GetBool("table")
		showAll, _ := cmd.Flags().GetBool("all")
		limit, _ := cmd.Flags().GetInt("limit")
		
		interval := time.Second
		count := 1
		
		if len(args) >= 1 {
			if i, err := strconv.Atoi(args[0]); err == nil && i > 0 {
				interval = time.Duration(i) * time.Second
				count = 0 // Infinite by default when interval is specified
			}
		}
		
		if len(args) >= 2 {
			if c, err := strconv.Atoi(args[1]); err == nil && c > 0 {
				count = c
			}
		}
		
		if jsonOutput {
			infos, err := commands.GetPidstatInfo(interval)
			if err != nil {
				fmt.Printf("Error: %v\n", err)
				return
			}
			
			// Limit results for JSON output
			if limit > 0 && len(infos) > limit {
				infos = infos[:limit]
			}
			
			jsonData, err := json.MarshalIndent(infos, "", "  ")
			if err != nil {
				fmt.Printf("Error formatting JSON: %v\n", err)
				return
			}
			fmt.Println(string(jsonData))
		} else if table {
			if err := commands.PrintPidstatTable(showAll, limit); err != nil {
				fmt.Printf("Error: %v\n", err)
			}
		} else {
			if err := commands.PrintPidstat(interval, count, showAll); err != nil {
				fmt.Printf("Error: %v\n", err)
			}
		}
	},
}

func init() {
	pidstatCmd.Flags().BoolP("all", "a", false, "显示所有进程（包括CPU使用率为0的）")
	pidstatCmd.Flags().BoolP("table", "t", false, "以表格格式显示")
	pidstatCmd.Flags().IntP("limit", "l", 20, "限制显示的进程数量")
	rootCmd.AddCommand(pidstatCmd)
}
