package sysinfo

import (
	"fmt"

	"github.com/spf13/cobra"
)

var rootCmd = &cobra.Command{
	Use:   "lil_box",
	Short: "Linux系统监控工具箱",
	Long: `Lil Box - 经典Linux系统监控命令的Go实现

这个工具实现了经典的Linux系统监控命令，包括：
- uptime    - 显示系统运行时间和负载
- free      - 显示内存使用情况
- vmstat    - 显示虚拟内存统计
- mpstat    - 显示CPU统计信息
- iostat    - 显示I/O统计信息
- sar       - 显示网络和TCP统计
- pidstat   - 显示进程统计信息
- top       - 显示实时进程信息
- dmesg     - 显示内核消息

使用 'lil_box help' 查看所有可用命令。`,
	Run: func(cmd *cobra.Command, args []string) {
		fmt.Println("欢迎使用 Lil Box Linux系统监控工具箱!")
		fmt.Println("使用 'lil_box help' 查看所有可用命令")
		fmt.Println()
		fmt.Println("经典命令:")
		fmt.Println("  uptime    显示系统运行时间和负载")
		fmt.Println("  free      显示内存使用情况")
		fmt.Println("  vmstat    显示虚拟内存统计")
		fmt.Println("  mpstat    显示CPU统计信息")
		fmt.Println("  iostat    显示I/O统计信息")
		fmt.Println("  sar       显示网络和TCP统计")
		fmt.Println("  pidstat   显示进程统计信息")
		fmt.Println("  top       显示实时进程信息")
		fmt.Println("  dmesg     显示内核消息")
	},
}

// Execute executes the root command
func Execute() error {
	return rootCmd.Execute()
}

func init() {
	// 这里可以添加全局标志
	rootCmd.PersistentFlags().BoolP("json", "j", false, "以JSON格式输出结果")
	rootCmd.PersistentFlags().BoolP("verbose", "v", false, "详细输出")
}
