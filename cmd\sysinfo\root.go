package sysinfo

import (
	"fmt"

	"github.com/spf13/cobra"
)

var rootCmd = &cobra.Command{
	Use:   "lil_box",
	Short: "系统信息查询工具箱",
	Long: `Lil Box - 一个简单易用的系统信息查询工具箱

这个工具可以帮助您快速查询系统的各种信息，包括：
- 内存使用情况
- CPU使用情况  
- 磁盘使用情况
- 网络接口信息
- 系统基本信息
- 进程信息

使用 'lil_box help' 查看所有可用命令。`,
	Run: func(cmd *cobra.Command, args []string) {
		fmt.Println("欢迎使用 Lil Box 系统信息工具箱!")
		fmt.Println("使用 'lil_box help' 查看所有可用命令")
		fmt.Println("使用 'lil_box all' 查看所有系统信息")
	},
}

// Execute executes the root command
func Execute() error {
	return rootCmd.Execute()
}

func init() {
	// 这里可以添加全局标志
	rootCmd.PersistentFlags().BoolP("json", "j", false, "以JSON格式输出结果")
	rootCmd.PersistentFlags().BoolP("verbose", "v", false, "详细输出")
}
