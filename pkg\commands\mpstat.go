package commands

import (
	"fmt"
	"lil_box/pkg/sampler"
	"time"

	"github.com/olekukonko/tablewriter"
	"os"
)

// MpstatInfo represents mpstat information for a single CPU
type MpstatInfo struct {
	CPU       string  `json:"cpu"`
	User      float64 `json:"user"`
	Nice      float64 `json:"nice"`
	System    float64 `json:"system"`
	IOWait    float64 `json:"iowait"`
	IRQ       float64 `json:"irq"`
	SoftIRQ   float64 `json:"softirq"`
	Steal     float64 `json:"steal"`
	Guest     float64 `json:"guest"`
	GuestNice float64 `json:"guest_nice"`
	Idle      float64 `json:"idle"`
}

// GetMpstatInfo retrieves CPU statistics for all CPUs
func GetMpstatInfo(interval time.Duration) ([]MpstatInfo, error) {
	s := sampler.NewSampler(interval)
	
	// Take first sample
	sample1, err := s.TakeSample()
	if err != nil {
		return nil, fmt.Errorf("failed to take first sample: %v", err)
	}

	// Wait for interval
	time.Sleep(interval)

	// Take second sample
	sample2, err := s.TakeSample()
	if err != nil {
		return nil, fmt.Errorf("failed to take second sample: %v", err)
	}

	// Calculate CPU rates
	cpuRates := sampler.CalculateCPURate(sample1, sample2)
	
	var mpstatInfos []MpstatInfo
	for _, rate := range cpuRates {
		info := MpstatInfo{
			CPU:       rate.CPU,
			User:      rate.User,
			Nice:      0, // gopsutil doesn't separate nice from user
			System:    rate.System,
			IOWait:    rate.IOWait,
			IRQ:       rate.IRQ,
			SoftIRQ:   rate.SoftIRQ,
			Steal:     rate.Steal,
			Guest:     rate.Guest,
			GuestNice: rate.GuestNice,
			Idle:      rate.Idle,
		}
		mpstatInfos = append(mpstatInfos, info)
	}

	return mpstatInfos, nil
}

// PrintMpstat prints CPU statistics in mpstat format
func PrintMpstat(interval time.Duration, count int, showAll bool) error {
	for i := 0; i < count || count == 0; i++ {
		if i > 0 {
			fmt.Println()
		}

		// Print timestamp
		fmt.Printf("%s\n", time.Now().Format("15:04:05"))

		infos, err := GetMpstatInfo(interval)
		if err != nil {
			return err
		}

		if showAll {
			// Print header
			fmt.Printf("%-8s %6s %6s %6s %6s %6s %6s %6s %6s %6s %6s\n",
				"CPU", "%usr", "%nice", "%sys", "%iowait", "%irq", "%soft", "%steal", "%guest", "%gnice", "%idle")

			// Print data for each CPU
			for _, info := range infos {
				cpuName := info.CPU
				if cpuName == "cpu-total" {
					cpuName = "all"
				}
				
				fmt.Printf("%-8s %6.2f %6.2f %6.2f %7.2f %6.2f %6.2f %7.2f %7.2f %7.2f %6.2f\n",
					cpuName,
					info.User,
					info.Nice,
					info.System,
					info.IOWait,
					info.IRQ,
					info.SoftIRQ,
					info.Steal,
					info.Guest,
					info.GuestNice,
					info.Idle,
				)
			}
		} else {
			// Show only average (first entry is usually total)
			if len(infos) > 0 {
				info := infos[0]
				fmt.Printf("%-8s %6s %6s %6s %6s %6s %6s %6s %6s %6s %6s\n",
					"CPU", "%usr", "%nice", "%sys", "%iowait", "%irq", "%soft", "%steal", "%guest", "%gnice", "%idle")
				
				fmt.Printf("%-8s %6.2f %6.2f %6.2f %7.2f %6.2f %6.2f %7.2f %7.2f %7.2f %6.2f\n",
					"all",
					info.User,
					info.Nice,
					info.System,
					info.IOWait,
					info.IRQ,
					info.SoftIRQ,
					info.Steal,
					info.Guest,
					info.GuestNice,
					info.Idle,
				)
			}
		}

		if count > 0 && i >= count-1 {
			break
		}

		if i < count-1 || count == 0 {
			time.Sleep(interval)
		}
	}

	return nil
}

// PrintMpstatTable prints CPU statistics in table format
func PrintMpstatTable(showAll bool) error {
	infos, err := GetMpstatInfo(time.Second)
	if err != nil {
		return err
	}

	table := tablewriter.NewWriter(os.Stdout)
	table.SetHeader([]string{"CPU", "%usr", "%nice", "%sys", "%iowait", "%irq", "%soft", "%steal", "%guest", "%gnice", "%idle"})

	if showAll {
		for _, info := range infos {
			cpuName := info.CPU
			if cpuName == "cpu-total" {
				cpuName = "all"
			}
			
			row := []string{
				cpuName,
				fmt.Sprintf("%.2f", info.User),
				fmt.Sprintf("%.2f", info.Nice),
				fmt.Sprintf("%.2f", info.System),
				fmt.Sprintf("%.2f", info.IOWait),
				fmt.Sprintf("%.2f", info.IRQ),
				fmt.Sprintf("%.2f", info.SoftIRQ),
				fmt.Sprintf("%.2f", info.Steal),
				fmt.Sprintf("%.2f", info.Guest),
				fmt.Sprintf("%.2f", info.GuestNice),
				fmt.Sprintf("%.2f", info.Idle),
			}
			table.Append(row)
		}
	} else {
		// Show only average
		if len(infos) > 0 {
			info := infos[0]
			row := []string{
				"all",
				fmt.Sprintf("%.2f", info.User),
				fmt.Sprintf("%.2f", info.Nice),
				fmt.Sprintf("%.2f", info.System),
				fmt.Sprintf("%.2f", info.IOWait),
				fmt.Sprintf("%.2f", info.IRQ),
				fmt.Sprintf("%.2f", info.SoftIRQ),
				fmt.Sprintf("%.2f", info.Steal),
				fmt.Sprintf("%.2f", info.Guest),
				fmt.Sprintf("%.2f", info.GuestNice),
				fmt.Sprintf("%.2f", info.Idle),
			}
			table.Append(row)
		}
	}

	table.Render()
	return nil
}
