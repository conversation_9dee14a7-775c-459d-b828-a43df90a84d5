package commands

import (
	"fmt"
	"lil_box/pkg/sampler"
	"time"

	"github.com/olekukonko/tablewriter"
	"os"
)

// IostatInfo represents iostat information for a device
type IostatInfo struct {
	Device      string  `json:"device"`
	TPS         float64 `json:"tps"`          // transfers per second
	ReadPS      float64 `json:"read_ps"`      // reads per second
	WritePS     float64 `json:"write_ps"`     // writes per second
	ReadKBPS    float64 `json:"read_kbps"`    // KB read per second
	WriteKBPS   float64 `json:"write_kbps"`   // KB written per second
	AvgReqSizeKB float64 `json:"avg_req_size_kb"` // average request size in KB
	AvgQueueLen float64 `json:"avg_queue_len"`    // average queue length
	AwaitMS     float64 `json:"await_ms"`         // average wait time in ms
	RAwaitMS    float64 `json:"r_await_ms"`       // average read wait time in ms
	WAwaitMS    float64 `json:"w_await_ms"`       // average write wait time in ms
	SvcTimeMS   float64 `json:"svc_time_ms"`      // average service time in ms
	Util        float64 `json:"util"`             // utilization percentage
}

// GetIostatInfo retrieves I/O statistics for all devices
func GetIostatInfo(interval time.Duration) ([]IostatInfo, error) {
	s := sampler.NewSampler(interval)
	
	// Take first sample
	sample1, err := s.TakeSample()
	if err != nil {
		return nil, fmt.Errorf("failed to take first sample: %v", err)
	}

	// Wait for interval
	time.Sleep(interval)

	// Take second sample
	sample2, err := s.TakeSample()
	if err != nil {
		return nil, fmt.Errorf("failed to take second sample: %v", err)
	}

	// Calculate disk rates
	diskRates := sampler.CalculateDiskRate(sample1, sample2)
	
	var iostatInfos []IostatInfo
	for _, rate := range diskRates {
		// Skip devices with no activity
		if rate.ReadPS == 0 && rate.WritePS == 0 {
			continue
		}

		tps := rate.ReadPS + rate.WritePS
		readKBPS := rate.ReadBPS / 1024
		writeKBPS := rate.WriteBPS / 1024
		
		// Calculate average request size
		var avgReqSize float64
		if tps > 0 {
			avgReqSize = (rate.ReadBPS + rate.WriteBPS) / tps / 1024
		}

		// Calculate service time (approximation)
		var svcTime float64
		if tps > 0 {
			svcTime = (rate.Util / 100) * 1000 / tps
		}

		info := IostatInfo{
			Device:       rate.Device,
			TPS:          tps,
			ReadPS:       rate.ReadPS,
			WritePS:      rate.WritePS,
			ReadKBPS:     readKBPS,
			WriteKBPS:    writeKBPS,
			AvgReqSizeKB: avgReqSize,
			AvgQueueLen:  rate.AvgQueueLen,
			AwaitMS:      rate.AwaitMS,
			RAwaitMS:     0, // Would need separate read/write timing
			WAwaitMS:     0, // Would need separate read/write timing
			SvcTimeMS:    svcTime,
			Util:         rate.Util,
		}
		iostatInfos = append(iostatInfos, info)
	}

	return iostatInfos, nil
}

// PrintIostat prints I/O statistics in iostat format
func PrintIostat(interval time.Duration, count int, extended bool) error {
	for i := 0; i < count || count == 0; i++ {
		if i > 0 {
			fmt.Println()
		}

		// Print timestamp
		fmt.Printf("%s\n", time.Now().Format("15:04:05"))

		infos, err := GetIostatInfo(interval)
		if err != nil {
			return err
		}

		if extended {
			// Extended format (-x option)
			fmt.Printf("%-10s %8s %8s %8s %8s %8s %8s %8s %8s %8s %8s\n",
				"Device", "r/s", "w/s", "rkB/s", "wkB/s", "rrqm/s", "wrqm/s", "avgqu-sz", "await", "svctm", "%util")

			for _, info := range infos {
				fmt.Printf("%-10s %8.2f %8.2f %8.2f %8.2f %8.2f %8.2f %8.2f %8.2f %8.2f %6.2f\n",
					info.Device,
					info.ReadPS,
					info.WritePS,
					info.ReadKBPS,
					info.WriteKBPS,
					0.0, // rrqm/s - read requests merged per second (not available in gopsutil)
					0.0, // wrqm/s - write requests merged per second (not available in gopsutil)
					info.AvgQueueLen,
					info.AwaitMS,
					info.SvcTimeMS,
					info.Util,
				)
			}
		} else {
			// Standard format
			fmt.Printf("%-10s %8s %8s %8s %8s %8s %8s\n",
				"Device", "tps", "kB_read/s", "kB_wrtn/s", "kB_read", "kB_wrtn", "avgqu-sz")

			for _, info := range infos {
				// For cumulative values, we'd need to track them over time
				// For now, just show rates
				fmt.Printf("%-10s %8.2f %8.2f %8.2f %8.0f %8.0f %8.2f\n",
					info.Device,
					info.TPS,
					info.ReadKBPS,
					info.WriteKBPS,
					info.ReadKBPS*float64(interval.Seconds()), // Approximate
					info.WriteKBPS*float64(interval.Seconds()), // Approximate
					info.AvgQueueLen,
				)
			}
		}

		if count > 0 && i >= count-1 {
			break
		}

		if i < count-1 || count == 0 {
			time.Sleep(interval)
		}
	}

	return nil
}

// PrintIostatTable prints I/O statistics in table format
func PrintIostatTable(extended bool) error {
	infos, err := GetIostatInfo(time.Second)
	if err != nil {
		return err
	}

	table := tablewriter.NewWriter(os.Stdout)
	
	if extended {
		table.SetHeader([]string{"Device", "r/s", "w/s", "rkB/s", "wkB/s", "avgqu-sz", "await", "svctm", "%util"})
		
		for _, info := range infos {
			row := []string{
				info.Device,
				fmt.Sprintf("%.2f", info.ReadPS),
				fmt.Sprintf("%.2f", info.WritePS),
				fmt.Sprintf("%.2f", info.ReadKBPS),
				fmt.Sprintf("%.2f", info.WriteKBPS),
				fmt.Sprintf("%.2f", info.AvgQueueLen),
				fmt.Sprintf("%.2f", info.AwaitMS),
				fmt.Sprintf("%.2f", info.SvcTimeMS),
				fmt.Sprintf("%.2f", info.Util),
			}
			table.Append(row)
		}
	} else {
		table.SetHeader([]string{"Device", "tps", "kB_read/s", "kB_wrtn/s", "avgqu-sz"})
		
		for _, info := range infos {
			row := []string{
				info.Device,
				fmt.Sprintf("%.2f", info.TPS),
				fmt.Sprintf("%.2f", info.ReadKBPS),
				fmt.Sprintf("%.2f", info.WriteKBPS),
				fmt.Sprintf("%.2f", info.AvgQueueLen),
			}
			table.Append(row)
		}
	}

	table.Render()
	return nil
}
