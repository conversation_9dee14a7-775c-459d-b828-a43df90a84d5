package commands

import (
	"fmt"
	"runtime"
	"time"

	"github.com/shirou/gopsutil/v3/host"
	"github.com/shirou/gopsutil/v3/load"
)

// UptimeInfo represents uptime information
type UptimeInfo struct {
	CurrentTime time.Time `json:"current_time"`
	Uptime      uint64    `json:"uptime_seconds"`
	UptimeStr   string    `json:"uptime_string"`
	Users       int       `json:"users"`
	LoadAvg1    float64   `json:"load_avg_1"`
	LoadAvg5    float64   `json:"load_avg_5"`
	LoadAvg15   float64   `json:"load_avg_15"`
}

// GetUptimeInfo retrieves system uptime information
func GetUptimeInfo() (*UptimeInfo, error) {
	hostInfo, err := host.Info()
	if err != nil {
		return nil, fmt.Errorf("failed to get host info: %v", err)
	}

	info := &UptimeInfo{
		CurrentTime: time.Now(),
		Uptime:      hostInfo.Uptime,
		UptimeStr:   formatUptime(hostInfo.Uptime),
		Users:       0, // gopsutil doesn't provide user count directly
	}

	// Get load averages (only available on Unix-like systems)
	if runtime.GOOS != "windows" {
		loadInfo, err := load.Avg()
		if err == nil {
			info.LoadAvg1 = loadInfo.Load1
			info.LoadAvg5 = loadInfo.Load5
			info.LoadAvg15 = loadInfo.Load15
		}
	}

	return info, nil
}

// formatUptime formats uptime seconds into human readable string
func formatUptime(seconds uint64) string {
	days := seconds / 86400
	hours := (seconds % 86400) / 3600
	minutes := (seconds % 3600) / 60

	if days > 0 {
		if days == 1 {
			return fmt.Sprintf("%d day, %d:%02d", days, hours, minutes)
		}
		return fmt.Sprintf("%d days, %d:%02d", days, hours, minutes)
	}
	return fmt.Sprintf("%d:%02d", hours, minutes)
}

// PrintUptime prints uptime information in the classic uptime format
func PrintUptime() error {
	info, err := GetUptimeInfo()
	if err != nil {
		return err
	}

	currentTime := info.CurrentTime.Format("15:04:05")
	
	if runtime.GOOS == "windows" {
		// Windows doesn't have load averages
		fmt.Printf(" %s up %s, load average: not available\n",
			currentTime, info.UptimeStr)
	} else {
		fmt.Printf(" %s up %s, load average: %.2f, %.2f, %.2f\n",
			currentTime, info.UptimeStr, info.LoadAvg1, info.LoadAvg5, info.LoadAvg15)
	}

	return nil
}
