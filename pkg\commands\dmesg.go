package commands

import (
	"bufio"
	"fmt"
	"os"
	"os/exec"
	"runtime"
	"strconv"
	"strings"
	"time"
)

// DmesgEntry represents a single dmesg log entry
type DmesgEntry struct {
	Timestamp time.Time `json:"timestamp"`
	Level     string    `json:"level"`
	Message   string    `json:"message"`
	Raw       string    `json:"raw"`
}

// GetDmesgEntries retrieves kernel messages
func GetDmesgEntries(tail int) ([]DmesgEntry, error) {
	var entries []DmesgEntry

	switch runtime.GOOS {
	case "linux":
		// Try to read from /dev/kmsg first (requires root)
		if entries, err := readKmsg(tail); err == nil {
			return entries, nil
		}
		
		// Fallback to /proc/kmsg (also requires root)
		if entries, err := readProcKmsg(tail); err == nil {
			return entries, nil
		}
		
		// Fallback to dmesg command
		return readDmesgCommand(tail)
		
	case "darwin":
		// macOS doesn't have dmesg, use system.log or unified logging
		return readMacOSLogs(tail)
		
	case "windows":
		// Windows doesn't have dmesg equivalent, use Event Log
		return readWindowsEventLog(tail)
		
	default:
		return nil, fmt.Errorf("dmesg not supported on %s", runtime.GOOS)
	}
}

// readKmsg reads from /dev/kmsg (Linux)
func readKmsg(tail int) ([]DmesgEntry, error) {
	file, err := os.Open("/dev/kmsg")
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var entries []DmesgEntry
	scanner := bufio.NewScanner(file)
	
	for scanner.Scan() {
		line := scanner.Text()
		entry := parseDmesgLine(line)
		entries = append(entries, entry)
		
		if tail > 0 && len(entries) >= tail {
			break
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, err
	}

	// Return last 'tail' entries
	if tail > 0 && len(entries) > tail {
		entries = entries[len(entries)-tail:]
	}

	return entries, nil
}

// readProcKmsg reads from /proc/kmsg (Linux)
func readProcKmsg(tail int) ([]DmesgEntry, error) {
	file, err := os.Open("/proc/kmsg")
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var entries []DmesgEntry
	scanner := bufio.NewScanner(file)
	
	for scanner.Scan() {
		line := scanner.Text()
		entry := parseDmesgLine(line)
		entries = append(entries, entry)
		
		if tail > 0 && len(entries) >= tail {
			break
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, err
	}

	return entries, nil
}

// readDmesgCommand uses the dmesg command as fallback
func readDmesgCommand(tail int) ([]DmesgEntry, error) {
	var cmd *exec.Cmd
	if tail > 0 {
		cmd = exec.Command("dmesg", "--time-format", "iso")
	} else {
		cmd = exec.Command("dmesg", "--time-format", "iso")
	}

	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("failed to execute dmesg: %v", err)
	}

	lines := strings.Split(string(output), "\n")
	var entries []DmesgEntry

	for _, line := range lines {
		if strings.TrimSpace(line) == "" {
			continue
		}
		entry := parseDmesgLine(line)
		entries = append(entries, entry)
	}

	// Return last 'tail' entries
	if tail > 0 && len(entries) > tail {
		entries = entries[len(entries)-tail:]
	}

	return entries, nil
}

// readMacOSLogs reads system logs on macOS
func readMacOSLogs(tail int) ([]DmesgEntry, error) {
	cmd := exec.Command("log", "show", "--predicate", "subsystem == 'com.apple.kernel'", "--style", "syslog")
	if tail > 0 {
		cmd.Args = append(cmd.Args, "--last", fmt.Sprintf("%dm", tail))
	}

	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("failed to execute log command: %v", err)
	}

	lines := strings.Split(string(output), "\n")
	var entries []DmesgEntry

	for _, line := range lines {
		if strings.TrimSpace(line) == "" {
			continue
		}
		entry := DmesgEntry{
			Timestamp: time.Now(),
			Level:     "info",
			Message:   line,
			Raw:       line,
		}
		entries = append(entries, entry)
	}

	return entries, nil
}

// readWindowsEventLog reads Windows Event Log
func readWindowsEventLog(tail int) ([]DmesgEntry, error) {
	// This is a simplified implementation
	// In practice, you'd use Windows API or PowerShell
	cmd := exec.Command("powershell", "-Command", 
		"Get-WinEvent -LogName System | Select-Object -First 100 | Format-Table -AutoSize")

	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("failed to execute PowerShell command: %v", err)
	}

	lines := strings.Split(string(output), "\n")
	var entries []DmesgEntry

	for _, line := range lines {
		if strings.TrimSpace(line) == "" {
			continue
		}
		entry := DmesgEntry{
			Timestamp: time.Now(),
			Level:     "info",
			Message:   line,
			Raw:       line,
		}
		entries = append(entries, entry)
	}

	if tail > 0 && len(entries) > tail {
		entries = entries[len(entries)-tail:]
	}

	return entries, nil
}

// parseDmesgLine parses a dmesg log line
func parseDmesgLine(line string) DmesgEntry {
	entry := DmesgEntry{
		Raw:       line,
		Timestamp: time.Now(),
		Level:     "info",
		Message:   line,
	}

	// Parse Linux dmesg format: [timestamp] message
	if strings.HasPrefix(line, "[") {
		endBracket := strings.Index(line, "]")
		if endBracket > 0 {
			timestampStr := line[1:endBracket]
			if timestamp, err := strconv.ParseFloat(timestampStr, 64); err == nil {
				// Convert from seconds since boot to actual time
				bootTime := time.Now().Add(-time.Duration(timestamp) * time.Second)
				entry.Timestamp = bootTime
			}
			entry.Message = strings.TrimSpace(line[endBracket+1:])
		}
	}

	// Parse log level from message
	if strings.Contains(entry.Message, "WARN") || strings.Contains(entry.Message, "warn") {
		entry.Level = "warning"
	} else if strings.Contains(entry.Message, "ERR") || strings.Contains(entry.Message, "error") {
		entry.Level = "error"
	} else if strings.Contains(entry.Message, "CRIT") || strings.Contains(entry.Message, "critical") {
		entry.Level = "critical"
	}

	return entry
}

// PrintDmesg prints kernel messages in dmesg format
func PrintDmesg(tail int, follow bool) error {
	if follow {
		// Continuous monitoring mode
		for {
			entries, err := GetDmesgEntries(10) // Get last 10 entries
			if err != nil {
				return err
			}

			for _, entry := range entries {
				fmt.Printf("[%s] %s\n", 
					entry.Timestamp.Format("2006-01-02 15:04:05"), 
					entry.Message)
			}

			time.Sleep(time.Second)
		}
	} else {
		entries, err := GetDmesgEntries(tail)
		if err != nil {
			return err
		}

		for _, entry := range entries {
			if runtime.GOOS == "linux" {
				// Linux-style output
				fmt.Printf("[%s] %s\n", 
					entry.Timestamp.Format("2006-01-02T15:04:05.000000-07:00"), 
					entry.Message)
			} else {
				// Generic output
				fmt.Printf("[%s] %s\n", 
					entry.Timestamp.Format("2006-01-02 15:04:05"), 
					entry.Message)
			}
		}
	}

	return nil
}

// PrintDmesgTail prints the last N kernel messages
func PrintDmesgTail(lines int) error {
	entries, err := GetDmesgEntries(lines)
	if err != nil {
		return err
	}

	for _, entry := range entries {
		fmt.Printf("[%s] %s\n", 
			entry.Timestamp.Format("2006-01-02 15:04:05"), 
			entry.Message)
	}

	return nil
}
