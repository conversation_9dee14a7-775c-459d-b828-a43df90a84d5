package commands

import (
	"fmt"
	"lil_box/pkg/sampler"
	"time"

	"github.com/olekukonko/tablewriter"
	"github.com/shirou/gopsutil/v3/net"
	"os"
)

// SarNetworkInfo represents network statistics for sar -n DEV
type SarNetworkInfo struct {
	Interface string  `json:"interface"`
	RxPckPS   float64 `json:"rxpck_ps"`   // packets received per second
	TxPckPS   float64 `json:"txpck_ps"`   // packets transmitted per second
	RxKBPS    float64 `json:"rxkb_ps"`    // KB received per second
	TxKBPS    float64 `json:"txkb_ps"`    // KB transmitted per second
	RxCmpPS   float64 `json:"rxcmp_ps"`   // compressed packets received per second
	TxCmpPS   float64 `json:"txcmp_ps"`   // compressed packets transmitted per second
	RxMcstPS  float64 `json:"rxmcst_ps"`  // multicast packets received per second
	RxErrPS   float64 `json:"rxerr_ps"`   // receive errors per second
	TxErrPS   float64 `json:"txerr_ps"`   // transmit errors per second
	ColPS     float64 `json:"col_ps"`     // collisions per second
	RxDropPS  float64 `json:"rxdrop_ps"`  // receive drops per second
	TxDropPS  float64 `json:"txdrop_ps"`  // transmit drops per second
	TxCarrPS  float64 `json:"txcarr_ps"`  // carrier errors per second
	RxFrmPS   float64 `json:"rxfrm_ps"`   // frame errors per second
	RxFifoPS  float64 `json:"rxfifo_ps"`  // FIFO errors per second
	TxFifoPS  float64 `json:"txfifo_ps"`  // FIFO errors per second
}

// SarTCPInfo represents TCP statistics for sar -n TCP,ETCP
type SarTCPInfo struct {
	ActiveOpens  float64 `json:"active_opens"`  // active connections openings per second
	PassiveOpens float64 `json:"passive_opens"` // passive connection openings per second
	AttemptFails float64 `json:"attempt_fails"` // failed connection attempts per second
	EstabResets  float64 `json:"estab_resets"`  // connection resets received per second
	CurrEstab    uint64  `json:"curr_estab"`    // current established connections
	InSegs       float64 `json:"in_segs"`       // segments received per second
	OutSegs      float64 `json:"out_segs"`      // segments sent per second
	RetransSegs  float64 `json:"retrans_segs"`  // segments retransmitted per second
	InErrs       float64 `json:"in_errs"`       // bad segments received per second
	OutRsts      float64 `json:"out_rsts"`      // resets sent per second
}

// GetSarNetworkInfo retrieves network statistics
func GetSarNetworkInfo(interval time.Duration) ([]SarNetworkInfo, error) {
	s := sampler.NewSampler(interval)
	
	// Take first sample
	sample1, err := s.TakeSample()
	if err != nil {
		return nil, fmt.Errorf("failed to take first sample: %v", err)
	}

	// Wait for interval
	time.Sleep(interval)

	// Take second sample
	sample2, err := s.TakeSample()
	if err != nil {
		return nil, fmt.Errorf("failed to take second sample: %v", err)
	}

	// Calculate network rates
	netRates := sampler.CalculateNetworkRate(sample1, sample2)
	
	var sarInfos []SarNetworkInfo
	for _, rate := range netRates {
		// Skip loopback and inactive interfaces
		if rate.Interface == "lo" || (rate.RxBPS == 0 && rate.TxBPS == 0) {
			continue
		}

		info := SarNetworkInfo{
			Interface: rate.Interface,
			RxPckPS:   rate.RxPPS,
			TxPckPS:   rate.TxPPS,
			RxKBPS:    rate.RxBPS / 1024,
			TxKBPS:    rate.TxBPS / 1024,
			RxCmpPS:   0, // Not available in gopsutil
			TxCmpPS:   0, // Not available in gopsutil
			RxMcstPS:  0, // Not available in gopsutil
			RxErrPS:   rate.RxErrPS,
			TxErrPS:   rate.TxErrPS,
			ColPS:     0, // Not available in gopsutil
			RxDropPS:  rate.RxDropPS,
			TxDropPS:  rate.TxDropPS,
			TxCarrPS:  0, // Not available in gopsutil
			RxFrmPS:   0, // Not available in gopsutil
			RxFifoPS:  0, // Not available in gopsutil
			TxFifoPS:  0, // Not available in gopsutil
		}
		sarInfos = append(sarInfos, info)
	}

	return sarInfos, nil
}

// GetSarTCPInfo retrieves TCP statistics
func GetSarTCPInfo() (*SarTCPInfo, error) {
	// Get current connection count
	connections, err := net.Connections("tcp")
	if err != nil {
		return nil, fmt.Errorf("failed to get TCP connections: %v", err)
	}

	var established uint64
	for _, conn := range connections {
		if conn.Status == "ESTABLISHED" {
			established++
		}
	}

	// Note: gopsutil doesn't provide detailed TCP statistics like
	// active/passive opens, retransmissions, etc. These would require
	// reading from /proc/net/snmp or /proc/net/netstat on Linux
	info := &SarTCPInfo{
		ActiveOpens:  0, // Would need /proc/net/snmp
		PassiveOpens: 0, // Would need /proc/net/snmp
		AttemptFails: 0, // Would need /proc/net/snmp
		EstabResets:  0, // Would need /proc/net/snmp
		CurrEstab:    established,
		InSegs:       0, // Would need /proc/net/snmp
		OutSegs:      0, // Would need /proc/net/snmp
		RetransSegs:  0, // Would need /proc/net/snmp
		InErrs:       0, // Would need /proc/net/snmp
		OutRsts:      0, // Would need /proc/net/snmp
	}

	return info, nil
}

// PrintSarNetwork prints network statistics in sar format
func PrintSarNetwork(interval time.Duration, count int) error {
	for i := 0; i < count || count == 0; i++ {
		if i > 0 {
			fmt.Println()
		}

		// Print timestamp
		fmt.Printf("%s\n", time.Now().Format("15:04:05"))

		infos, err := GetSarNetworkInfo(interval)
		if err != nil {
			return err
		}

		// Print header
		fmt.Printf("%-10s %8s %8s %8s %8s %8s %8s %8s %8s\n",
			"IFACE", "rxpck/s", "txpck/s", "rxkB/s", "txkB/s", "rxerr/s", "txerr/s", "rxdrop/s", "txdrop/s")

		// Print data
		for _, info := range infos {
			fmt.Printf("%-10s %8.2f %8.2f %8.2f %8.2f %8.2f %8.2f %8.2f %8.2f\n",
				info.Interface,
				info.RxPckPS,
				info.TxPckPS,
				info.RxKBPS,
				info.TxKBPS,
				info.RxErrPS,
				info.TxErrPS,
				info.RxDropPS,
				info.TxDropPS,
			)
		}

		if count > 0 && i >= count-1 {
			break
		}

		if i < count-1 || count == 0 {
			time.Sleep(interval)
		}
	}

	return nil
}

// PrintSarTCP prints TCP statistics in sar format
func PrintSarTCP(interval time.Duration, count int) error {
	for i := 0; i < count || count == 0; i++ {
		if i > 0 {
			fmt.Println()
		}

		// Print timestamp
		fmt.Printf("%s\n", time.Now().Format("15:04:05"))

		info, err := GetSarTCPInfo()
		if err != nil {
			return err
		}

		// Print header
		fmt.Printf("%8s %8s %8s %8s %8s %8s %8s %8s %8s %8s\n",
			"active/s", "passive/s", "iseg/s", "oseg/s", "atmptf/s", "estres/s", "retrans/s", "isegerr/s", "orsts/s", "estab")

		// Print data
		fmt.Printf("%8.2f %8.2f %8.2f %8.2f %8.2f %8.2f %8.2f %8.2f %8.2f %8d\n",
			info.ActiveOpens,
			info.PassiveOpens,
			info.InSegs,
			info.OutSegs,
			info.AttemptFails,
			info.EstabResets,
			info.RetransSegs,
			info.InErrs,
			info.OutRsts,
			info.CurrEstab,
		)

		if count > 0 && i >= count-1 {
			break
		}

		if i < count-1 || count == 0 {
			time.Sleep(interval)
		}
	}

	return nil
}

// PrintSarNetworkTable prints network statistics in table format
func PrintSarNetworkTable() error {
	infos, err := GetSarNetworkInfo(time.Second)
	if err != nil {
		return err
	}

	table := tablewriter.NewWriter(os.Stdout)
	table.SetHeader([]string{"Interface", "rxpck/s", "txpck/s", "rxkB/s", "txkB/s", "rxerr/s", "txerr/s", "rxdrop/s", "txdrop/s"})

	for _, info := range infos {
		row := []string{
			info.Interface,
			fmt.Sprintf("%.2f", info.RxPckPS),
			fmt.Sprintf("%.2f", info.TxPckPS),
			fmt.Sprintf("%.2f", info.RxKBPS),
			fmt.Sprintf("%.2f", info.TxKBPS),
			fmt.Sprintf("%.2f", info.RxErrPS),
			fmt.Sprintf("%.2f", info.TxErrPS),
			fmt.Sprintf("%.2f", info.RxDropPS),
			fmt.Sprintf("%.2f", info.TxDropPS),
		}
		table.Append(row)
	}

	table.Render()
	return nil
}
