package commands

import (
	"fmt"
	"lil_box/pkg/sampler"
	"runtime"
	"time"

	"github.com/olekukonko/tablewriter"
	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/shirou/gopsutil/v3/mem"
	"os"
)

// VmstatInfo represents vmstat information
type VmstatInfo struct {
	// Procs
	ProcsRunning  uint64 `json:"procs_running"`
	ProcsBlocked  uint64 `json:"procs_blocked"`
	
	// Memory (KB)
	MemSwpd       uint64 `json:"mem_swpd"`
	MemFree       uint64 `json:"mem_free"`
	MemBuff       uint64 `json:"mem_buff"`
	MemCache      uint64 `json:"mem_cache"`
	
	// Swap (KB/s)
	SwapIn        float64 `json:"swap_in"`
	SwapOut       float64 `json:"swap_out"`
	
	// IO (blocks/s)
	BlockIn       float64 `json:"block_in"`
	BlockOut      float64 `json:"block_out"`
	
	// System
	Interrupts    float64 `json:"interrupts"`
	ContextSwitch float64 `json:"context_switch"`
	
	// CPU (%)
	CPUUser       float64 `json:"cpu_user"`
	CPUSystem     float64 `json:"cpu_system"`
	CPUIdle       float64 `json:"cpu_idle"`
	CPUWait       float64 `json:"cpu_wait"`
	CPUSteal      float64 `json:"cpu_steal"`
}

// GetVmstatInfo retrieves vmstat information
func GetVmstatInfo() (*VmstatInfo, error) {
	// Get memory info
	vmem, err := mem.VirtualMemory()
	if err != nil {
		return nil, fmt.Errorf("failed to get virtual memory info: %v", err)
	}

	swap, err := mem.SwapMemory()
	if err != nil {
		return nil, fmt.Errorf("failed to get swap memory info: %v", err)
	}

	info := &VmstatInfo{
		// Memory in KB
		MemSwpd:  swap.Used / 1024,
		MemFree:  vmem.Free / 1024,
		MemBuff:  vmem.Buffers / 1024,
		MemCache: vmem.Cached / 1024,
		
		// These require sampling for rates, set to 0 for now
		ProcsRunning:  0,
		ProcsBlocked:  0,
		SwapIn:        0,
		SwapOut:       0,
		BlockIn:       0,
		BlockOut:      0,
		Interrupts:    0,
		ContextSwitch: 0,
		CPUUser:       0,
		CPUSystem:     0,
		CPUIdle:       0,
		CPUWait:       0,
		CPUSteal:      0,
	}

	return info, nil
}

// GetVmstatInfoWithSampling retrieves vmstat information with rate calculations
func GetVmstatInfoWithSampling(interval time.Duration) (*VmstatInfo, error) {
	s := sampler.NewSampler(interval)
	
	// Take first sample
	sample1, err := s.TakeSample()
	if err != nil {
		return nil, fmt.Errorf("failed to take first sample: %v", err)
	}

	// Wait for interval
	time.Sleep(interval)

	// Take second sample
	sample2, err := s.TakeSample()
	if err != nil {
		return nil, fmt.Errorf("failed to take second sample: %v", err)
	}

	// Get basic memory info
	info, err := GetVmstatInfo()
	if err != nil {
		return nil, err
	}

	// Calculate CPU rates
	cpuRates := sampler.CalculateCPURate(sample1, sample2)
	if len(cpuRates) > 0 {
		// Use overall CPU stats (first entry is usually "cpu-total")
		var totalUser, totalSystem, totalIdle, totalWait, totalSteal float64
		count := 0
		
		for _, rate := range cpuRates {
			if rate.CPU != "cpu-total" && rate.CPU != "cpu" {
				totalUser += rate.User
				totalSystem += rate.System
				totalIdle += rate.Idle
				totalWait += rate.IOWait
				totalSteal += rate.Steal
				count++
			}
		}
		
		if count > 0 {
			info.CPUUser = totalUser / float64(count)
			info.CPUSystem = totalSystem / float64(count)
			info.CPUIdle = totalIdle / float64(count)
			info.CPUWait = totalWait / float64(count)
			info.CPUSteal = totalSteal / float64(count)
		}
	}

	// Get CPU stats for context switches and interrupts
	if runtime.GOOS == "linux" {
		cpuStat, err := cpu.Info()
		if err == nil && len(cpuStat) > 0 {
			// These values are cumulative, we'd need to sample to get rates
			// For now, just show 0 as we don't have previous values
		}
	}

	return info, nil
}

// PrintVmstat prints vmstat information
func PrintVmstat(interval time.Duration, count int) error {
	// Print header
	fmt.Println("procs -----------memory---------- ---swap-- -----io---- -system-- ------cpu-----")
	fmt.Println(" r  b   swpd   free   buff  cache   si   so    bi    bo   in   cs us sy id wa st")

	for i := 0; i < count || count == 0; i++ {
		var info *VmstatInfo
		var err error

		if i == 0 {
			// First iteration shows current state
			info, err = GetVmstatInfo()
		} else {
			// Subsequent iterations show rates
			info, err = GetVmstatInfoWithSampling(interval)
		}

		if err != nil {
			return err
		}

		// Print the data row
		fmt.Printf("%2d %2d %6d %6d %6d %6d %4.0f %4.0f %5.0f %5.0f %4.0f %4.0f %2.0f %2.0f %2.0f %2.0f %2.0f\n",
			info.ProcsRunning,
			info.ProcsBlocked,
			info.MemSwpd,
			info.MemFree,
			info.MemBuff,
			info.MemCache,
			info.SwapIn,
			info.SwapOut,
			info.BlockIn,
			info.BlockOut,
			info.Interrupts,
			info.ContextSwitch,
			info.CPUUser,
			info.CPUSystem,
			info.CPUIdle,
			info.CPUWait,
			info.CPUSteal,
		)

		if count > 0 && i >= count-1 {
			break
		}

		if i < count-1 || count == 0 {
			time.Sleep(interval)
		}
	}

	return nil
}

// PrintVmstatTable prints vmstat information in table format
func PrintVmstatTable() error {
	info, err := GetVmstatInfo()
	if err != nil {
		return err
	}

	table := tablewriter.NewWriter(os.Stdout)
	table.SetHeader([]string{"Metric", "Value", "Unit"})

	data := [][]string{
		{"Swap Used", fmt.Sprintf("%d", info.MemSwpd), "KB"},
		{"Memory Free", fmt.Sprintf("%d", info.MemFree), "KB"},
		{"Memory Buffers", fmt.Sprintf("%d", info.MemBuff), "KB"},
		{"Memory Cache", fmt.Sprintf("%d", info.MemCache), "KB"},
		{"CPU User", fmt.Sprintf("%.1f", info.CPUUser), "%"},
		{"CPU System", fmt.Sprintf("%.1f", info.CPUSystem), "%"},
		{"CPU Idle", fmt.Sprintf("%.1f", info.CPUIdle), "%"},
		{"CPU I/O Wait", fmt.Sprintf("%.1f", info.CPUWait), "%"},
	}

	for _, row := range data {
		table.Append(row)
	}

	table.Render()
	return nil
}
