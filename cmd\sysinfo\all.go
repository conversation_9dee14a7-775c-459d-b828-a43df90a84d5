package sysinfo

import (
	"encoding/json"
	"fmt"

	"lil_box/pkg/sysinfo"

	"github.com/spf13/cobra"
)

// AllInfo represents all system information
type AllInfo struct {
	System  *sysinfo.SystemInfo    `json:"system"`
	Memory  *sysinfo.MemoryInfo    `json:"memory"`
	CPU     *sysinfo.CPUInfo       `json:"cpu"`
	Disk    []sysinfo.DiskInfo     `json:"disk"`
	Network []sysinfo.NetworkInfo  `json:"network"`
	Process []sysinfo.ProcessInfo  `json:"top_processes"`
}

var allCmd = &cobra.Command{
	Use:   "all",
	Short: "查看所有系统信息",
	Long:  "一次性显示所有系统信息，包括内存、CPU、磁盘、网络、系统和进程信息",
	Aliases: []string{"a"},
	Run: func(cmd *cobra.Command, args []string) {
		jsonOutput, _ := cmd.Flags().GetBool("json")
		processLimit, _ := cmd.Flags().GetInt("process-limit")
		
		if jsonOutput {
			allInfo := &AllInfo{}
			
			// 获取系统信息
			if systemInfo, err := sysinfo.GetSystemInfo(); err == nil {
				allInfo.System = systemInfo
			}
			
			// 获取内存信息
			if memoryInfo, err := sysinfo.GetMemoryInfo(); err == nil {
				allInfo.Memory = memoryInfo
			}
			
			// 获取CPU信息
			if cpuInfo, err := sysinfo.GetCPUInfo(); err == nil {
				allInfo.CPU = cpuInfo
			}
			
			// 获取磁盘信息
			if diskInfo, err := sysinfo.GetDiskInfo(); err == nil {
				allInfo.Disk = diskInfo
			}
			
			// 获取网络信息
			if networkInfo, err := sysinfo.GetNetworkInfo(); err == nil {
				allInfo.Network = networkInfo
			}
			
			// 获取进程信息
			if processInfo, err := sysinfo.GetTopProcesses(processLimit); err == nil {
				allInfo.Process = processInfo
			}
			
			jsonData, err := json.MarshalIndent(allInfo, "", "  ")
			if err != nil {
				fmt.Printf("Error formatting JSON: %v\n", err)
				return
			}
			fmt.Println(string(jsonData))
		} else {
			// 显示所有信息
			fmt.Println("🖥️  正在收集系统信息...")
			fmt.Println()
			
			if err := sysinfo.PrintSystemInfo(); err != nil {
				fmt.Printf("系统信息获取失败: %v\n", err)
			}
			fmt.Println()
			
			if err := sysinfo.PrintMemoryInfo(); err != nil {
				fmt.Printf("内存信息获取失败: %v\n", err)
			}
			fmt.Println()
			
			if err := sysinfo.PrintCPUInfo(); err != nil {
				fmt.Printf("CPU信息获取失败: %v\n", err)
			}
			fmt.Println()
			
			if err := sysinfo.PrintDiskInfo(); err != nil {
				fmt.Printf("磁盘信息获取失败: %v\n", err)
			}
			fmt.Println()
			
			if err := sysinfo.PrintNetworkInfo(); err != nil {
				fmt.Printf("网络信息获取失败: %v\n", err)
			}
			fmt.Println()
			
			if err := sysinfo.PrintTopProcesses(processLimit); err != nil {
				fmt.Printf("进程信息获取失败: %v\n", err)
			}
		}
	},
}

func init() {
	allCmd.Flags().IntP("process-limit", "p", 10, "显示的进程数量限制")
	rootCmd.AddCommand(allCmd)
}
