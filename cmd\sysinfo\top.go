package sysinfo

import (
	"encoding/json"
	"fmt"
	"lil_box/pkg/commands"
	"time"

	"github.com/spf13/cobra"
)

// formatUptime formats uptime seconds into human readable string
func formatUptime(seconds uint64) string {
	days := seconds / 86400
	hours := (seconds % 86400) / 3600
	minutes := (seconds % 3600) / 60

	if days > 0 {
		if days == 1 {
			return fmt.Sprintf("%d day, %d:%02d", days, hours, minutes)
		}
		return fmt.Sprintf("%d days, %d:%02d", days, hours, minutes)
	}
	return fmt.Sprintf("%d:%02d", hours, minutes)
}

var topCmd = &cobra.Command{
	Use:   "top [options]",
	Short: "显示实时进程信息",
	Long:  "显示实时进程信息，类似于Linux的top命令",
	Run: func(cmd *cobra.Command, args []string) {
		jsonOutput, _ := cmd.Flags().GetBool("json")
		iterations, _ := cmd.Flags().GetInt("iterations")
		delay, _ := cmd.Flags().GetInt("delay")
		batch, _ := cmd.Flags().GetBool("batch")

		delayDuration := time.Duration(delay) * time.Second

		if jsonOutput || batch {
			// Single snapshot for JSON or batch mode
			info, err := commands.GetTopInfo()
			if err != nil {
				fmt.Printf("Error: %v\n", err)
				return
			}

			if jsonOutput {
				jsonData, err := json.MarshalIndent(info, "", "  ")
				if err != nil {
					fmt.Printf("Error formatting JSON: %v\n", err)
					return
				}
				fmt.Println(string(jsonData))
			} else {
				// Batch mode - print once without clearing screen
				fmt.Printf("top - %s up %s, load average: %.2f, %.2f, %.2f\n",
					time.Now().Format("15:04:05"),
					formatUptime(info.Uptime),
					info.LoadAvg1, info.LoadAvg5, info.LoadAvg15)

				fmt.Printf("Tasks: %3d total, %3d running, %3d sleeping, %3d stopped, %3d zombie\n",
					info.Tasks, info.Running, info.Sleeping, info.Stopped, info.Zombie)

				fmt.Printf("%%Cpu(s): %5.1f us, %5.1f sy, %5.1f ni, %5.1f id, %5.1f wa\n",
					info.CPUUser, info.CPUSystem, 0.0, info.CPUIdle, info.CPUWait)

				fmt.Printf("MiB Mem : %8.1f total, %8.1f free, %8.1f used, %8.1f buff/cache\n",
					float64(info.MemTotal)/1024/1024,
					float64(info.MemFree)/1024/1024,
					float64(info.MemUsed)/1024/1024,
					float64(info.MemBuffCache)/1024/1024)

				fmt.Printf("MiB Swap: %8.1f total, %8.1f free, %8.1f used.\n",
					float64(info.SwapTotal)/1024/1024,
					float64(info.SwapFree)/1024/1024,
					float64(info.SwapUsed)/1024/1024)

				fmt.Println()
				fmt.Printf("%7s %-8s %2s %3s %7s %7s %7s %1s %6s %6s %9s %s\n",
					"PID", "USER", "PR", "NI", "VIRT", "RES", "SHR", "S", "%CPU", "%MEM", "TIME+", "COMMAND")

				for _, proc := range info.Processes {
					user := proc.User
					if len(user) > 8 {
						user = user[:8]
					}

					command := proc.Command
					if len(command) > 15 {
						command = command[:12] + "..."
					}

					fmt.Printf("%7d %-8s %2d %3d %7d %7d %7d %1s %6.1f %6.1f %9s %s\n",
						proc.PID, user, proc.Priority, proc.Nice,
						proc.VIRT, proc.RES, proc.SHR, proc.Status,
						proc.CPUPercent, proc.MemPercent, proc.Time, command)
				}
			}
		} else {
			// Interactive mode
			if err := commands.PrintTop(iterations, delayDuration); err != nil {
				fmt.Printf("Error: %v\n", err)
			}
		}
	},
}

func init() {
	topCmd.Flags().IntP("iterations", "n", 0, "迭代次数 (0表示无限)")
	topCmd.Flags().IntP("delay", "d", 3, "刷新间隔（秒）")
	topCmd.Flags().BoolP("batch", "b", false, "批处理模式（非交互式）")
	rootCmd.AddCommand(topCmd)
}
