package sysinfo

import (
	"encoding/json"
	"fmt"

	"lil_box/pkg/sysinfo"

	"github.com/spf13/cobra"
)

var networkCmd = &cobra.Command{
	Use:   "network",
	Short: "查看网络信息",
	Long:  "显示网络接口信息和连接统计",
	Aliases: []string{"net", "n"},
	Run: func(cmd *cobra.Command, args []string) {
		jsonOutput, _ := cmd.Flags().GetBool("json")
		showConnections, _ := cmd.Flags().GetBool("connections")
		
		if jsonOutput {
			if showConnections {
				info, err := sysinfo.GetConnectionInfo()
				if err != nil {
					fmt.Printf("Error: %v\n", err)
					return
				}
				jsonData, err := json.MarshalIndent(info, "", "  ")
				if err != nil {
					fmt.Printf("Error formatting JSON: %v\n", err)
					return
				}
				fmt.Println(string(jsonData))
			} else {
				info, err := sysinfo.GetNetworkInfo()
				if err != nil {
					fmt.Printf("Error: %v\n", err)
					return
				}
				jsonData, err := json.MarshalIndent(info, "", "  ")
				if err != nil {
					fmt.Printf("Error formatting JSON: %v\n", err)
					return
				}
				fmt.Println(string(jsonData))
			}
		} else {
			if showConnections {
				if err := sysinfo.PrintConnectionInfo(); err != nil {
					fmt.Printf("Error: %v\n", err)
				}
			} else {
				if err := sysinfo.PrintNetworkInfo(); err != nil {
					fmt.Printf("Error: %v\n", err)
				}
			}
		}
	},
}

func init() {
	networkCmd.Flags().BoolP("connections", "c", false, "显示网络连接信息")
	rootCmd.AddCommand(networkCmd)
}
