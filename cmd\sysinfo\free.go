package sysinfo

import (
	"encoding/json"
	"fmt"
	"lil_box/pkg/commands"

	"github.com/spf13/cobra"
)

var freeCmd = &cobra.Command{
	Use:   "free",
	Short: "显示内存使用情况",
	Long:  "显示系统内存使用情况，类似于Linux的free命令",
	Run: func(cmd *cobra.Command, args []string) {
		jsonOutput, _ := cmd.Flags().GetBool("json")
		humanReadable, _ := cmd.Flags().GetBool("human")
		megabytes, _ := cmd.Flags().GetBool("mega")
		
		if jsonOutput {
			info, err := commands.GetFreeInfo()
			if err != nil {
				fmt.Printf("Error: %v\n", err)
				return
			}
			
			jsonData, err := json.MarshalIndent(info, "", "  ")
			if err != nil {
				fmt.Printf("Error formatting JSON: %v\n", err)
				return
			}
			fmt.Println(string(jsonData))
		} else {
			if err := commands.PrintFree(humanReadable, megabytes); err != nil {
				fmt.Printf("Error: %v\n", err)
			}
		}
	},
}

func init() {
	freeCmd.Flags().BoolP("human", "h", false, "以人类可读格式显示")
	freeCmd.Flags().BoolP("mega", "m", false, "以MB为单位显示")
	rootCmd.AddCommand(freeCmd)
}
