package sysinfo

import (
	"encoding/json"
	"fmt"

	"lil_box/pkg/sysinfo"

	"github.com/spf13/cobra"
)

var systemCmd = &cobra.Command{
	Use:   "system",
	Short: "查看系统信息",
	Long:  "显示系统基本信息，包括主机名、操作系统、内核版本等",
	Aliases: []string{"sys", "s"},
	Run: func(cmd *cobra.Command, args []string) {
		jsonOutput, _ := cmd.Flags().GetBool("json")
		
		if jsonOutput {
			info, err := sysinfo.GetSystemInfo()
			if err != nil {
				fmt.Printf("Error: %v\n", err)
				return
			}
			
			jsonData, err := json.MarshalIndent(info, "", "  ")
			if err != nil {
				fmt.Printf("Error formatting JSON: %v\n", err)
				return
			}
			fmt.Println(string(jsonData))
		} else {
			if err := sysinfo.PrintSystemInfo(); err != nil {
				fmt.Printf("Error: %v\n", err)
			}
		}
	},
}

var processCmd = &cobra.Command{
	Use:   "process",
	Short: "查看进程信息",
	Long:  "显示系统进程信息，按CPU使用率排序",
	Aliases: []string{"proc", "ps", "p"},
	Run: func(cmd *cobra.Command, args []string) {
		jsonOutput, _ := cmd.Flags().GetBool("json")
		limit, _ := cmd.Flags().GetInt("limit")
		
		if jsonOutput {
			info, err := sysinfo.GetTopProcesses(limit)
			if err != nil {
				fmt.Printf("Error: %v\n", err)
				return
			}
			
			jsonData, err := json.MarshalIndent(info, "", "  ")
			if err != nil {
				fmt.Printf("Error formatting JSON: %v\n", err)
				return
			}
			fmt.Println(string(jsonData))
		} else {
			if err := sysinfo.PrintTopProcesses(limit); err != nil {
				fmt.Printf("Error: %v\n", err)
			}
		}
	},
}

func init() {
	processCmd.Flags().IntP("limit", "l", 10, "显示进程数量限制")
	
	rootCmd.AddCommand(systemCmd)
	rootCmd.AddCommand(processCmd)
}
