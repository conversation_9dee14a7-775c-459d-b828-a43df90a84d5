package sysinfo

import (
	"fmt"
	"lil_box/pkg/utils"

	"github.com/shirou/gopsutil/v3/mem"
)

// MemoryInfo represents memory information
type MemoryInfo struct {
	Total       uint64  `json:"total"`
	Available   uint64  `json:"available"`
	Used        uint64  `json:"used"`
	Free        uint64  `json:"free"`
	UsedPercent float64 `json:"used_percent"`
	SwapTotal   uint64  `json:"swap_total"`
	SwapUsed    uint64  `json:"swap_used"`
	SwapPercent float64 `json:"swap_percent"`
}

// GetMemoryInfo retrieves memory information
func GetMemoryInfo() (*MemoryInfo, error) {
	// 获取虚拟内存信息
	v, err := mem.VirtualMemory()
	if err != nil {
		return nil, fmt.Errorf("获取内存信息失败: %v", err)
	}

	info := &MemoryInfo{
		Total:       v.Total,
		Available:   v.Available,
		Used:        v.Used,
		Free:        v.Free,
		UsedPercent: v.UsedPercent,
	}

	// 获取交换空间信息
	s, err := mem.SwapMemory()
	if err == nil {
		info.SwapTotal = s.Total
		info.SwapUsed = s.Used
		info.SwapPercent = s.UsedPercent
	}

	return info, nil
}

// PrintMemoryInfo prints formatted memory information
func PrintMemoryInfo() error {
	info, err := GetMemoryInfo()
	if err != nil {
		return err
	}

	fmt.Println("========== 内存统计信息 ==========")
	fmt.Printf("总内存 (Total):      %s\n", utils.FormatBytes(info.Total))
	fmt.Printf("可用内存 (Available): %s  <-- (SRE应重点关注此项)\n", utils.FormatBytes(info.Available))
	fmt.Printf("已用内存 (Used):      %s\n", utils.FormatBytes(info.Used))
	fmt.Printf("空闲内存 (Free):      %s\n", utils.FormatBytes(info.Free))
	fmt.Println("-----------------------------------------")
	fmt.Printf("内存使用率:             %.2f %%\n", info.UsedPercent)
	fmt.Println("-----------------------------------------")

	if info.SwapTotal > 0 {
		fmt.Printf("总交换空间 (SwapTotal):  %s\n", utils.FormatBytes(info.SwapTotal))
		fmt.Printf("已用交换空间 (SwapUsed): %s\n", utils.FormatBytes(info.SwapUsed))
		fmt.Printf("交换空间使用率:        %.2f %%\n", info.SwapPercent)
	} else {
		fmt.Println("交换空间: 未配置")
	}

	return nil
}
