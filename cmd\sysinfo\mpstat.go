package sysinfo

import (
	"encoding/json"
	"fmt"
	"lil_box/pkg/commands"
	"strconv"
	"time"

	"github.com/spf13/cobra"
)

var mpstatCmd = &cobra.Command{
	Use:   "mpstat [interval] [count]",
	Short: "显示CPU统计信息",
	Long:  "显示CPU统计信息，类似于Linux的mpstat命令",
	Args:  cobra.MaximumNArgs(2),
	Run: func(cmd *cobra.Command, args []string) {
		jsonOutput, _ := cmd.Flags().GetBool("json")
		table, _ := cmd.Flags().GetBool("table")
		showAll, _ := cmd.Flags().GetBool("all")
		
		interval := time.Second
		count := 1
		
		if len(args) >= 1 {
			if i, err := strconv.Atoi(args[0]); err == nil && i > 0 {
				interval = time.Duration(i) * time.Second
				count = 0 // Infinite by default when interval is specified
			}
		}
		
		if len(args) >= 2 {
			if c, err := strconv.Atoi(args[1]); err == nil && c > 0 {
				count = c
			}
		}
		
		if jsonOutput {
			infos, err := commands.GetMpstatInfo(interval)
			if err != nil {
				fmt.Printf("Error: %v\n", err)
				return
			}
			
			jsonData, err := json.MarshalIndent(infos, "", "  ")
			if err != nil {
				fmt.Printf("Error formatting JSON: %v\n", err)
				return
			}
			fmt.Println(string(jsonData))
		} else if table {
			if err := commands.PrintMpstatTable(showAll); err != nil {
				fmt.Printf("Error: %v\n", err)
			}
		} else {
			if err := commands.PrintMpstat(interval, count, showAll); err != nil {
				fmt.Printf("Error: %v\n", err)
			}
		}
	},
}

func init() {
	mpstatCmd.Flags().BoolP("all", "P", false, "显示所有CPU核心 (-P ALL)")
	mpstatCmd.Flags().BoolP("table", "t", false, "以表格格式显示")
	rootCmd.AddCommand(mpstatCmd)
}
