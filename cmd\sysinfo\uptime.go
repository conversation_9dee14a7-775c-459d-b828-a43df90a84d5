package sysinfo

import (
	"encoding/json"
	"fmt"
	"lil_box/pkg/commands"

	"github.com/spf13/cobra"
)

var uptimeCmd = &cobra.Command{
	Use:   "uptime",
	Short: "显示系统运行时间和负载",
	Long:  "显示系统运行时间、用户数和负载平均值，类似于Linux的uptime命令",
	Run: func(cmd *cobra.Command, args []string) {
		jsonOutput, _ := cmd.Flags().GetBool("json")
		
		if jsonOutput {
			info, err := commands.GetUptimeInfo()
			if err != nil {
				fmt.Printf("Error: %v\n", err)
				return
			}
			
			jsonData, err := json.MarshalIndent(info, "", "  ")
			if err != nil {
				fmt.Printf("Error formatting JSON: %v\n", err)
				return
			}
			fmt.Println(string(jsonData))
		} else {
			if err := commands.PrintUptime(); err != nil {
				fmt.Printf("Error: %v\n", err)
			}
		}
	},
}

func init() {
	rootCmd.AddCommand(uptimeCmd)
}
