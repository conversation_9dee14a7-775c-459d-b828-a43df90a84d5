package sysinfo

import (
	"fmt"
	"lil_box/pkg/utils"
	"runtime"
	"time"

	"github.com/shirou/gopsutil/v3/host"
	"github.com/shirou/gopsutil/v3/process"
)

// SystemInfo represents system information
type SystemInfo struct {
	Hostname        string `json:"hostname"`
	OS              string `json:"os"`
	Platform        string `json:"platform"`
	PlatformFamily  string `json:"platform_family"`
	PlatformVersion string `json:"platform_version"`
	KernelVersion   string `json:"kernel_version"`
	KernelArch      string `json:"kernel_arch"`
	Uptime          uint64 `json:"uptime"`
	BootTime        uint64 `json:"boot_time"`
	Procs           uint64 `json:"procs"`
	GoVersion       string `json:"go_version"`
	GoArch          string `json:"go_arch"`
	GoOS            string `json:"go_os"`
}

// ProcessInfo represents process information
type ProcessInfo struct {
	PID     int32   `json:"pid"`
	Name    string  `json:"name"`
	Status  string  `json:"status"`
	CPU     float64 `json:"cpu"`
	Memory  float32 `json:"memory"`
	Command string  `json:"command"`
}

// GetSystemInfo retrieves system information
func GetSystemInfo() (*SystemInfo, error) {
	hostInfo, err := host.Info()
	if err != nil {
		return nil, fmt.Errorf("获取主机信息失败: %v", err)
	}

	info := &SystemInfo{
		Hostname:        hostInfo.Hostname,
		OS:              hostInfo.OS,
		Platform:        hostInfo.Platform,
		PlatformFamily:  hostInfo.PlatformFamily,
		PlatformVersion: hostInfo.PlatformVersion,
		KernelVersion:   hostInfo.KernelVersion,
		KernelArch:      hostInfo.KernelArch,
		Uptime:          hostInfo.Uptime,
		BootTime:        hostInfo.BootTime,
		Procs:           hostInfo.Procs,
		GoVersion:       runtime.Version(),
		GoArch:          runtime.GOARCH,
		GoOS:            runtime.GOOS,
	}

	return info, nil
}

// GetTopProcesses retrieves top processes by CPU usage
func GetTopProcesses(limit int) ([]ProcessInfo, error) {
	pids, err := process.Pids()
	if err != nil {
		return nil, fmt.Errorf("获取进程列表失败: %v", err)
	}

	var processes []ProcessInfo
	for _, pid := range pids {
		proc, err := process.NewProcess(pid)
		if err != nil {
			continue
		}

		name, err := proc.Name()
		if err != nil {
			continue
		}

		status, err := proc.Status()
		if err != nil {
			status = []string{"unknown"}
		}

		// 取第一个状态作为字符串
		statusStr := "unknown"
		if len(status) > 0 {
			statusStr = status[0]
		}

		cpuPercent, err := proc.CPUPercent()
		if err != nil {
			cpuPercent = 0
		}

		memPercent, err := proc.MemoryPercent()
		if err != nil {
			memPercent = 0
		}

		cmdline, err := proc.Cmdline()
		if err != nil {
			cmdline = ""
		}

		processInfo := ProcessInfo{
			PID:     pid,
			Name:    name,
			Status:  statusStr,
			CPU:     cpuPercent,
			Memory:  memPercent,
			Command: cmdline,
		}
		processes = append(processes, processInfo)
	}

	// 简单排序：按CPU使用率降序
	for i := 0; i < len(processes)-1; i++ {
		for j := i + 1; j < len(processes); j++ {
			if processes[i].CPU < processes[j].CPU {
				processes[i], processes[j] = processes[j], processes[i]
			}
		}
	}

	// 限制返回数量
	if limit > 0 && limit < len(processes) {
		processes = processes[:limit]
	}

	return processes, nil
}

// PrintSystemInfo prints formatted system information
func PrintSystemInfo() error {
	info, err := GetSystemInfo()
	if err != nil {
		return err
	}

	fmt.Println("========== 系统信息 ==========")
	fmt.Printf("主机名:               %s\n", info.Hostname)
	fmt.Printf("操作系统:             %s\n", info.OS)
	fmt.Printf("平台:                 %s\n", info.Platform)
	fmt.Printf("平台版本:             %s\n", info.PlatformVersion)
	fmt.Printf("内核版本:             %s\n", info.KernelVersion)
	fmt.Printf("架构:                 %s\n", info.KernelArch)
	fmt.Printf("运行时间:             %s\n", utils.FormatDuration(info.Uptime))
	fmt.Printf("启动时间:             %s\n", time.Unix(int64(info.BootTime), 0).Format("2006-01-02 15:04:05"))
	fmt.Printf("进程数量:             %d\n", info.Procs)
	fmt.Println("-----------------------------------------")
	fmt.Printf("Go版本:               %s\n", info.GoVersion)
	fmt.Printf("Go架构:               %s\n", info.GoArch)
	fmt.Printf("Go操作系统:           %s\n", info.GoOS)

	return nil
}

// PrintTopProcesses prints top processes by CPU usage
func PrintTopProcesses(limit int) error {
	processes, err := GetTopProcesses(limit)
	if err != nil {
		return err
	}

	fmt.Printf("========== TOP %d 进程 (按CPU使用率) ==========\n", limit)
	fmt.Printf("%-8s %-20s %-10s %-8s %-8s %s\n", "PID", "名称", "状态", "CPU%", "内存%", "命令行")
	fmt.Println("--------------------------------------------------------------------------------")

	for _, proc := range processes {
		command := proc.Command
		if len(command) > 40 {
			command = command[:37] + "..."
		}
		fmt.Printf("%-8d %-20s %-10s %-8.2f %-8.2f %s\n",
			proc.PID, proc.Name, proc.Status, proc.CPU, proc.Memory, command)
	}

	return nil
}
