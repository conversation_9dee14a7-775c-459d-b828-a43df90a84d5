package commands

import (
	"fmt"
	"sort"
	"time"

	"github.com/olekukonko/tablewriter"
	"github.com/shirou/gopsutil/v3/process"
	"os"
)

// PidstatInfo represents process statistics
type PidstatInfo struct {
	PID         int32   `json:"pid"`
	User        string  `json:"user"`
	Command     string  `json:"command"`
	CPUPercent  float64 `json:"cpu_percent"`
	MemPercent  float32 `json:"mem_percent"`
	VSZ         uint64  `json:"vsz"`         // Virtual memory size in KB
	RSS         uint64  `json:"rss"`         // Resident set size in KB
	MinorFaults uint64  `json:"minor_faults"` // Minor page faults
	MajorFaults uint64  `json:"major_faults"` // Major page faults
	ReadBytes   uint64  `json:"read_bytes"`   // Bytes read
	WriteBytes  uint64  `json:"write_bytes"`  // Bytes written
	Status      string  `json:"status"`
}

// GetPidstatInfo retrieves process statistics
func GetPidstatInfo(interval time.Duration) ([]PidstatInfo, error) {
	pids, err := process.Pids()
	if err != nil {
		return nil, fmt.Errorf("failed to get process list: %v", err)
	}

	var pidstatInfos []PidstatInfo

	for _, pid := range pids {
		proc, err := process.NewProcess(pid)
		if err != nil {
			continue
		}

		// Get process info
		name, err := proc.Name()
		if err != nil {
			continue
		}

		username, err := proc.Username()
		if err != nil {
			username = "unknown"
		}

		status, err := proc.Status()
		if err != nil {
			status = []string{"unknown"}
		}
		statusStr := "unknown"
		if len(status) > 0 {
			statusStr = status[0]
		}

		// Get CPU percentage (this requires a brief wait)
		cpuPercent, err := proc.CPUPercent()
		if err != nil {
			cpuPercent = 0
		}

		// Get memory info
		memInfo, err := proc.MemoryInfo()
		if err != nil {
			continue
		}

		memPercent, err := proc.MemoryPercent()
		if err != nil {
			memPercent = 0
		}

		// Get I/O info
		ioCounters, err := proc.IOCounters()
		var readBytes, writeBytes uint64
		if err == nil {
			readBytes = ioCounters.ReadBytes
			writeBytes = ioCounters.WriteBytes
		}

		// Get page fault info
		var minorFaults, majorFaults uint64
		// Note: gopsutil doesn't directly provide page fault info
		// This would require platform-specific implementation

		info := PidstatInfo{
			PID:         pid,
			User:        username,
			Command:     name,
			CPUPercent:  cpuPercent,
			MemPercent:  memPercent,
			VSZ:         memInfo.VMS / 1024, // Convert to KB
			RSS:         memInfo.RSS / 1024, // Convert to KB
			MinorFaults: minorFaults,
			MajorFaults: majorFaults,
			ReadBytes:   readBytes,
			WriteBytes:  writeBytes,
			Status:      statusStr,
		}

		pidstatInfos = append(pidstatInfos, info)
	}

	// Sort by CPU usage (descending)
	sort.Slice(pidstatInfos, func(i, j int) bool {
		return pidstatInfos[i].CPUPercent > pidstatInfos[j].CPUPercent
	})

	return pidstatInfos, nil
}

// PrintPidstat prints process statistics in pidstat format
func PrintPidstat(interval time.Duration, count int, showAll bool) error {
	for i := 0; i < count || count == 0; i++ {
		if i > 0 {
			fmt.Println()
		}

		// Print timestamp
		fmt.Printf("%s\n", time.Now().Format("15:04:05"))

		infos, err := GetPidstatInfo(interval)
		if err != nil {
			return err
		}

		// Filter out processes with 0% CPU if not showing all
		if !showAll {
			var filtered []PidstatInfo
			for _, info := range infos {
				if info.CPUPercent > 0 {
					filtered = append(filtered, info)
				}
			}
			infos = filtered
		}

		// Limit to top 20 processes
		if len(infos) > 20 {
			infos = infos[:20]
		}

		// Print header
		fmt.Printf("%8s %8s %8s %8s %8s %8s %-16s\n",
			"PID", "USER", "%CPU", "%MEM", "VSZ", "RSS", "COMMAND")

		// Print data
		for _, info := range infos {
			// Truncate username if too long
			user := info.User
			if len(user) > 8 {
				user = user[:8]
			}

			// Truncate command if too long
			command := info.Command
			if len(command) > 16 {
				command = command[:13] + "..."
			}

			fmt.Printf("%8d %8s %8.1f %8.1f %8d %8d %-16s\n",
				info.PID,
				user,
				info.CPUPercent,
				info.MemPercent,
				info.VSZ,
				info.RSS,
				command,
			)
		}

		if count > 0 && i >= count-1 {
			break
		}

		if i < count-1 || count == 0 {
			time.Sleep(interval)
		}
	}

	return nil
}

// PrintPidstatTable prints process statistics in table format
func PrintPidstatTable(showAll bool, limit int) error {
	infos, err := GetPidstatInfo(time.Second)
	if err != nil {
		return err
	}

	// Filter out processes with 0% CPU if not showing all
	if !showAll {
		var filtered []PidstatInfo
		for _, info := range infos {
			if info.CPUPercent > 0 {
				filtered = append(filtered, info)
			}
		}
		infos = filtered
	}

	// Limit results
	if limit > 0 && len(infos) > limit {
		infos = infos[:limit]
	}

	table := tablewriter.NewWriter(os.Stdout)
	table.SetHeader([]string{"PID", "USER", "%CPU", "%MEM", "VSZ(KB)", "RSS(KB)", "STATUS", "COMMAND"})

	for _, info := range infos {
		row := []string{
			fmt.Sprintf("%d", info.PID),
			info.User,
			fmt.Sprintf("%.1f", info.CPUPercent),
			fmt.Sprintf("%.1f", info.MemPercent),
			fmt.Sprintf("%d", info.VSZ),
			fmt.Sprintf("%d", info.RSS),
			info.Status,
			info.Command,
		}
		table.Append(row)
	}

	table.Render()
	return nil
}
