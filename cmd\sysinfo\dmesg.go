package sysinfo

import (
	"encoding/json"
	"fmt"
	"lil_box/pkg/commands"

	"github.com/spf13/cobra"
)

var dmesgCmd = &cobra.Command{
	Use:   "dmesg [options]",
	Short: "显示内核消息",
	Long:  "显示内核消息，类似于Linux的dmesg命令",
	Run: func(cmd *cobra.Command, args []string) {
		jsonOutput, _ := cmd.Flags().GetBool("json")
		tail, _ := cmd.Flags().GetInt("tail")
		follow, _ := cmd.Flags().GetBool("follow")
		
		if jsonOutput {
			entries, err := commands.GetDmesgEntries(tail)
			if err != nil {
				fmt.Printf("Error: %v\n", err)
				return
			}
			
			jsonData, err := json.MarshalIndent(entries, "", "  ")
			if err != nil {
				fmt.Printf("Error formatting JSON: %v\n", err)
				return
			}
			fmt.Println(string(jsonData))
		} else {
			if tail > 0 && !follow {
				if err := commands.PrintDmesgTail(tail); err != nil {
					fmt.Printf("Error: %v\n", err)
				}
			} else {
				if err := commands.PrintDmesg(tail, follow); err != nil {
					fmt.Printf("Error: %v\n", err)
				}
			}
		}
	},
}

func init() {
	dmesgCmd.Flags().IntP("tail", "T", 0, "显示最后N行消息")
	dmesgCmd.Flags().BoolP("follow", "f", false, "持续监控新消息")
	rootCmd.AddCommand(dmesgCmd)
}
