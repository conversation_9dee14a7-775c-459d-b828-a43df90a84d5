package sysinfo

import (
	"fmt"
	"lil_box/pkg/utils"

	"github.com/shirou/gopsutil/v3/net"
)

// NetworkInfo represents network interface information
type NetworkInfo struct {
	Name        string `json:"name"`
	BytesSent   uint64 `json:"bytes_sent"`
	BytesRecv   uint64 `json:"bytes_recv"`
	PacketsSent uint64 `json:"packets_sent"`
	PacketsRecv uint64 `json:"packets_recv"`
	Errin       uint64 `json:"errin"`
	Errout      uint64 `json:"errout"`
	Dropin      uint64 `json:"dropin"`
	Dropout     uint64 `json:"dropout"`
}

// ConnectionInfo represents network connection information
type ConnectionInfo struct {
	Type   string `json:"type"`
	Status string `json:"status"`
	Count  int    `json:"count"`
}

// GetNetworkInfo retrieves network interface statistics
func GetNetworkInfo() ([]NetworkInfo, error) {
	ioCounters, err := net.IOCounters(true)
	if err != nil {
		return nil, fmt.Errorf("获取网络接口信息失败: %v", err)
	}

	var networkInfos []NetworkInfo
	for _, counter := range ioCounters {
		// 跳过回环接口和无流量的接口
		if counter.Name == "lo" || (counter.BytesSent == 0 && counter.BytesRecv == 0) {
			continue
		}

		networkInfo := NetworkInfo{
			Name:        counter.Name,
			BytesSent:   counter.BytesSent,
			BytesRecv:   counter.BytesRecv,
			PacketsSent: counter.PacketsSent,
			PacketsRecv: counter.PacketsRecv,
			Errin:       counter.Errin,
			Errout:      counter.Errout,
			Dropin:      counter.Dropin,
			Dropout:     counter.Dropout,
		}
		networkInfos = append(networkInfos, networkInfo)
	}

	return networkInfos, nil
}

// GetConnectionInfo retrieves network connection statistics
func GetConnectionInfo() ([]ConnectionInfo, error) {
	connections, err := net.Connections("all")
	if err != nil {
		return nil, fmt.Errorf("获取网络连接信息失败: %v", err)
	}

	// 统计连接状态
	statusCount := make(map[string]int)
	typeCount := make(map[string]int)

	for _, conn := range connections {
		statusCount[conn.Status]++
		// 将uint32类型转换为字符串
		connType := fmt.Sprintf("%d", conn.Type)
		switch conn.Type {
		case 1:
			connType = "TCP"
		case 2:
			connType = "UDP"
		case 3:
			connType = "TCP6"
		case 4:
			connType = "UDP6"
		default:
			connType = fmt.Sprintf("TYPE_%d", conn.Type)
		}
		typeCount[connType]++
	}

	var connectionInfos []ConnectionInfo

	// 添加按类型统计的信息
	for connType, count := range typeCount {
		connectionInfos = append(connectionInfos, ConnectionInfo{
			Type:   connType,
			Status: "ALL",
			Count:  count,
		})
	}

	// 添加按状态统计的信息
	for status, count := range statusCount {
		if status != "" {
			connectionInfos = append(connectionInfos, ConnectionInfo{
				Type:   "TCP",
				Status: status,
				Count:  count,
			})
		}
	}

	return connectionInfos, nil
}

// PrintNetworkInfo prints formatted network information
func PrintNetworkInfo() error {
	networkInfos, err := GetNetworkInfo()
	if err != nil {
		return err
	}

	fmt.Println("========== 网络接口信息 ==========")
	for _, info := range networkInfos {
		fmt.Printf("接口名称: %s\n", info.Name)
		fmt.Printf("发送字节: %s\n", utils.FormatBytes(info.BytesSent))
		fmt.Printf("接收字节: %s\n", utils.FormatBytes(info.BytesRecv))
		fmt.Printf("发送包数: %d\n", info.PacketsSent)
		fmt.Printf("接收包数: %d\n", info.PacketsRecv)
		if info.Errin > 0 || info.Errout > 0 {
			fmt.Printf("接收错误: %d\n", info.Errin)
			fmt.Printf("发送错误: %d\n", info.Errout)
		}
		if info.Dropin > 0 || info.Dropout > 0 {
			fmt.Printf("接收丢包: %d\n", info.Dropin)
			fmt.Printf("发送丢包: %d\n", info.Dropout)
		}
		fmt.Println("-----------------------------------------")
	}

	return nil
}

// PrintConnectionInfo prints formatted connection information
func PrintConnectionInfo() error {
	connectionInfos, err := GetConnectionInfo()
	if err != nil {
		return err
	}

	fmt.Println("========== 网络连接信息 ==========")

	// 按类型分组显示
	typeConnections := make(map[string][]ConnectionInfo)
	for _, info := range connectionInfos {
		if info.Status == "ALL" {
			fmt.Printf("%s 连接总数: %d\n", info.Type, info.Count)
		} else {
			typeConnections[info.Type] = append(typeConnections[info.Type], info)
		}
	}

	// 显示TCP连接状态详情
	if tcpConns, exists := typeConnections["TCP"]; exists {
		fmt.Println("\nTCP连接状态详情:")
		for _, conn := range tcpConns {
			fmt.Printf("  %s: %d\n", conn.Status, conn.Count)
		}
	}

	return nil
}
