package commands

import (
	"fmt"
	"runtime"
	"sort"
	"time"

	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/shirou/gopsutil/v3/host"
	"github.com/shirou/gopsutil/v3/load"
	"github.com/shirou/gopsutil/v3/mem"
	"github.com/shirou/gopsutil/v3/process"
)

// TopInfo represents top command information
type TopInfo struct {
	Uptime       uint64              `json:"uptime"`
	LoadAvg1     float64             `json:"load_avg_1"`
	LoadAvg5     float64             `json:"load_avg_5"`
	LoadAvg15    float64             `json:"load_avg_15"`
	Tasks        int                 `json:"tasks"`
	Running      int                 `json:"running"`
	Sleeping     int                 `json:"sleeping"`
	Stopped      int                 `json:"stopped"`
	Zombie       int                 `json:"zombie"`
	CPUUser      float64             `json:"cpu_user"`
	CPUSystem    float64             `json:"cpu_system"`
	CPUIdle      float64             `json:"cpu_idle"`
	CPUWait      float64             `json:"cpu_wait"`
	MemTotal     uint64              `json:"mem_total"`
	MemFree      uint64              `json:"mem_free"`
	MemUsed      uint64              `json:"mem_used"`
	MemBuffCache uint64              `json:"mem_buff_cache"`
	SwapTotal    uint64              `json:"swap_total"`
	SwapFree     uint64              `json:"swap_free"`
	SwapUsed     uint64              `json:"swap_used"`
	Processes    []TopProcessInfo    `json:"processes"`
}

// TopProcessInfo represents process information for top
type TopProcessInfo struct {
	PID        int32   `json:"pid"`
	User       string  `json:"user"`
	Priority   int32   `json:"priority"`
	Nice       int32   `json:"nice"`
	VIRT       uint64  `json:"virt"`       // Virtual memory in KB
	RES        uint64  `json:"res"`        // Resident memory in KB
	SHR        uint64  `json:"shr"`        // Shared memory in KB
	Status     string  `json:"status"`
	CPUPercent float64 `json:"cpu_percent"`
	MemPercent float32 `json:"mem_percent"`
	Time       string  `json:"time"`       // CPU time
	Command    string  `json:"command"`
}

// GetTopInfo retrieves top command information
func GetTopInfo() (*TopInfo, error) {
	info := &TopInfo{}

	// Get host info
	hostInfo, err := host.Info()
	if err != nil {
		return nil, fmt.Errorf("failed to get host info: %v", err)
	}
	info.Uptime = hostInfo.Uptime

	// Get load averages (Unix-like systems only)
	if runtime.GOOS != "windows" {
		loadInfo, err := load.Avg()
		if err == nil {
			info.LoadAvg1 = loadInfo.Load1
			info.LoadAvg5 = loadInfo.Load5
			info.LoadAvg15 = loadInfo.Load15
		}
	}

	// Get CPU info
	cpuPercents, err := cpu.Percent(time.Second, false)
	if err == nil && len(cpuPercents) > 0 {
		info.CPUIdle = cpuPercents[0]
		info.CPUUser = 100 - cpuPercents[0] // Simplified
	}

	// Get memory info
	vmem, err := mem.VirtualMemory()
	if err == nil {
		info.MemTotal = vmem.Total
		info.MemFree = vmem.Free
		info.MemUsed = vmem.Used
		info.MemBuffCache = vmem.Buffers + vmem.Cached
	}

	// Get swap info
	swap, err := mem.SwapMemory()
	if err == nil {
		info.SwapTotal = swap.Total
		info.SwapFree = swap.Free
		info.SwapUsed = swap.Used
	}

	// Get process info
	processes, err := GetTopProcesses()
	if err == nil {
		info.Processes = processes
		
		// Count process states
		for _, proc := range processes {
			info.Tasks++
			switch proc.Status {
			case "R", "running":
				info.Running++
			case "S", "sleeping":
				info.Sleeping++
			case "T", "stopped":
				info.Stopped++
			case "Z", "zombie":
				info.Zombie++
			default:
				info.Sleeping++ // Default to sleeping
			}
		}
	}

	return info, nil
}

// GetTopProcesses retrieves process information for top display
func GetTopProcesses() ([]TopProcessInfo, error) {
	pids, err := process.Pids()
	if err != nil {
		return nil, fmt.Errorf("failed to get process list: %v", err)
	}

	var processes []TopProcessInfo

	for _, pid := range pids {
		proc, err := process.NewProcess(pid)
		if err != nil {
			continue
		}

		name, err := proc.Name()
		if err != nil {
			continue
		}

		username, err := proc.Username()
		if err != nil {
			username = "unknown"
		}

		status, err := proc.Status()
		if err != nil {
			status = []string{"unknown"}
		}
		statusStr := "S" // Default to sleeping
		if len(status) > 0 {
			switch status[0] {
			case "running":
				statusStr = "R"
			case "sleeping":
				statusStr = "S"
			case "stopped":
				statusStr = "T"
			case "zombie":
				statusStr = "Z"
			default:
				statusStr = "S"
			}
		}

		cpuPercent, err := proc.CPUPercent()
		if err != nil {
			cpuPercent = 0
		}

		memInfo, err := proc.MemoryInfo()
		if err != nil {
			continue
		}

		memPercent, err := proc.MemoryPercent()
		if err != nil {
			memPercent = 0
		}

		// Get nice value
		nice, err := proc.Nice()
		if err != nil {
			nice = 0
		}

		// Get CPU times for time display
		cpuTimes, err := proc.Times()
		var timeStr string
		if err == nil {
			totalTime := cpuTimes.User + cpuTimes.System
			minutes := int(totalTime) / 60
			seconds := int(totalTime) % 60
			timeStr = fmt.Sprintf("%d:%02d", minutes, seconds)
		} else {
			timeStr = "0:00"
		}

		processInfo := TopProcessInfo{
			PID:        pid,
			User:       username,
			Priority:   20, // Default priority
			Nice:       nice,
			VIRT:       memInfo.VMS / 1024,  // Convert to KB
			RES:        memInfo.RSS / 1024,  // Convert to KB
			SHR:        0,                   // Shared memory not available in gopsutil
			Status:     statusStr,
			CPUPercent: cpuPercent,
			MemPercent: memPercent,
			Time:       timeStr,
			Command:    name,
		}

		processes = append(processes, processInfo)
	}

	// Sort by CPU usage (descending)
	sort.Slice(processes, func(i, j int) bool {
		return processes[i].CPUPercent > processes[j].CPUPercent
	})

	// Limit to top 20 processes
	if len(processes) > 20 {
		processes = processes[:20]
	}

	return processes, nil
}

// PrintTop prints top information in classic top format
func PrintTop(iterations int, delay time.Duration) error {
	for i := 0; i < iterations || iterations == 0; i++ {
		// Clear screen (simplified)
		fmt.Print("\033[2J\033[H")

		info, err := GetTopInfo()
		if err != nil {
			return err
		}

		// Print header
		currentTime := time.Now().Format("15:04:05")
		fmt.Printf("top - %s up %s, %d users, load average: %.2f, %.2f, %.2f\n",
			currentTime,
			formatUptime(info.Uptime),
			0, // User count not available
			info.LoadAvg1, info.LoadAvg5, info.LoadAvg15)

		fmt.Printf("Tasks: %3d total, %3d running, %3d sleeping, %3d stopped, %3d zombie\n",
			info.Tasks, info.Running, info.Sleeping, info.Stopped, info.Zombie)

		fmt.Printf("%%Cpu(s): %5.1f us, %5.1f sy, %5.1f ni, %5.1f id, %5.1f wa, %5.1f hi, %5.1f si, %5.1f st\n",
			info.CPUUser, info.CPUSystem, 0.0, info.CPUIdle, info.CPUWait, 0.0, 0.0, 0.0)

		fmt.Printf("MiB Mem : %8.1f total, %8.1f free, %8.1f used, %8.1f buff/cache\n",
			float64(info.MemTotal)/1024/1024,
			float64(info.MemFree)/1024/1024,
			float64(info.MemUsed)/1024/1024,
			float64(info.MemBuffCache)/1024/1024)

		fmt.Printf("MiB Swap: %8.1f total, %8.1f free, %8.1f used.\n",
			float64(info.SwapTotal)/1024/1024,
			float64(info.SwapFree)/1024/1024,
			float64(info.SwapUsed)/1024/1024)

		fmt.Println()

		// Print process header
		fmt.Printf("%7s %-8s %2s %3s %7s %7s %7s %1s %6s %6s %9s %s\n",
			"PID", "USER", "PR", "NI", "VIRT", "RES", "SHR", "S", "%CPU", "%MEM", "TIME+", "COMMAND")

		// Print processes
		for _, proc := range info.Processes {
			user := proc.User
			if len(user) > 8 {
				user = user[:8]
			}

			command := proc.Command
			if len(command) > 15 {
				command = command[:12] + "..."
			}

			fmt.Printf("%7d %-8s %2d %3d %7d %7d %7d %1s %6.1f %6.1f %9s %s\n",
				proc.PID,
				user,
				proc.Priority,
				proc.Nice,
				proc.VIRT,
				proc.RES,
				proc.SHR,
				proc.Status,
				proc.CPUPercent,
				proc.MemPercent,
				proc.Time,
				command)
		}

		if iterations > 0 && i >= iterations-1 {
			break
		}

		time.Sleep(delay)
	}

	return nil
}
