# Development Guide

## Prerequisites

- Go 1.21 or later
- Git
- Make (optional, for using Makefile commands)

## Development Setup

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd lil_box
   ```

2. Install dependencies:
   ```bash
   go mod tidy
   ```

3. Install development tools:
   ```bash
   make install-tools
   ```

## Project Structure

- `cmd/` - Main applications for this project
  - `server/` - HTTP server application
- `internal/` - Private application and library code
  - `config/` - Configuration management
- `pkg/` - Library code that's ok to use by external applications
  - `utils/` - Utility functions
- `api/` - OpenAPI/Swagger specs, JSON schema files, protocol definition files
- `web/` - Web application specific components (static files, templates)
- `configs/` - Configuration file templates or default configs
- `scripts/` - Scripts to perform various build, install, analysis, etc operations
- `test/` - Additional external test apps and test data
- `docs/` - Design and user documents
- `bin/` - Compiled binaries (generated)

## Development Workflow

### Running the Application

```bash
# Run the main application
go run main.go

# Run the server
go run cmd/server/main.go

# Or use make
make run
```

### Testing

```bash
# Run all tests
go test ./...

# Run tests with coverage
make test-coverage

# Run specific package tests
go test ./pkg/utils
```

### Building

```bash
# Build for current platform
make build

# Build for Linux
make build-linux

# Build using script
./scripts/build.sh
```

### Code Quality

```bash
# Format code
make fmt

# Lint code
make lint
```

## Configuration

The application can be configured using:
- Environment variables
- Configuration files in `configs/` directory
- Command line flags (if implemented)

## API Documentation

API documentation is available in `api/openapi.yaml`. You can view it using tools like:
- Swagger UI
- Redoc
- Postman

## Contributing

1. Create a feature branch
2. Make your changes
3. Add tests for new functionality
4. Ensure all tests pass
5. Format and lint your code
6. Submit a pull request
