package commands

import (
	"fmt"

	"github.com/olekukonko/tablewriter"
	"github.com/shirou/gopsutil/v3/mem"
	"os"
)

// FreeInfo represents memory information in free command format
type FreeInfo struct {
	MemTotal     uint64 `json:"mem_total"`
	MemUsed      uint64 `json:"mem_used"`
	MemFree      uint64 `json:"mem_free"`
	MemShared    uint64 `json:"mem_shared"`
	MemBuffCache uint64 `json:"mem_buff_cache"`
	MemAvailable uint64 `json:"mem_available"`
	SwapTotal    uint64 `json:"swap_total"`
	SwapUsed     uint64 `json:"swap_used"`
	SwapFree     uint64 `json:"swap_free"`
}

// GetFreeInfo retrieves memory information
func GetFreeInfo() (*FreeInfo, error) {
	// Get virtual memory info
	vmem, err := mem.VirtualMemory()
	if err != nil {
		return nil, fmt.Erro<PERSON>("failed to get virtual memory info: %v", err)
	}

	// Get swap memory info
	swap, err := mem.SwapMemory()
	if err != nil {
		return nil, fmt.Errorf("failed to get swap memory info: %v", err)
	}

	info := &FreeInfo{
		MemTotal:     vmem.Total,
		MemUsed:      vmem.Used,
		MemFree:      vmem.Free,
		MemShared:    vmem.Shared,
		MemBuffCache: vmem.Buffers + vmem.Cached,
		MemAvailable: vmem.Available,
		SwapTotal:    swap.Total,
		SwapUsed:     swap.Used,
		SwapFree:     swap.Free,
	}

	return info, nil
}

// PrintFree prints memory information in free command format
func PrintFree(humanReadable bool, megabytes bool) error {
	info, err := GetFreeInfo()
	if err != nil {
		return err
	}

	var divisor uint64 = 1
	var unit string = ""

	if megabytes {
		divisor = 1024 * 1024
		unit = "Mi"
	} else if humanReadable {
		divisor = 1024
		unit = "Ki"
	}

	// Create table
	table := tablewriter.NewWriter(os.Stdout)
	table.SetHeader([]string{"", fmt.Sprintf("total%s", unit), fmt.Sprintf("used%s", unit), 
		fmt.Sprintf("free%s", unit), fmt.Sprintf("shared%s", unit), 
		fmt.Sprintf("buff/cache%s", unit), fmt.Sprintf("available%s", unit)})
	
	table.SetBorder(false)
	table.SetHeaderLine(false)
	table.SetColumnSeparator(" ")
	table.SetAlignment(tablewriter.ALIGN_RIGHT)
	table.SetHeaderAlignment(tablewriter.ALIGN_LEFT)

	// Memory row
	memRow := []string{
		"Mem:",
		formatMemory(info.MemTotal, divisor),
		formatMemory(info.MemUsed, divisor),
		formatMemory(info.MemFree, divisor),
		formatMemory(info.MemShared, divisor),
		formatMemory(info.MemBuffCache, divisor),
		formatMemory(info.MemAvailable, divisor),
	}
	table.Append(memRow)

	// Swap row
	swapRow := []string{
		"Swap:",
		formatMemory(info.SwapTotal, divisor),
		formatMemory(info.SwapUsed, divisor),
		formatMemory(info.SwapFree, divisor),
		"-",
		"-",
		"-",
	}
	table.Append(swapRow)

	table.Render()
	return nil
}

// formatMemory formats memory value according to divisor
func formatMemory(value, divisor uint64) string {
	if divisor == 1 {
		return fmt.Sprintf("%d", value)
	}
	return fmt.Sprintf("%d", value/divisor)
}
