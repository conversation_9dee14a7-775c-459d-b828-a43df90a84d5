package utils

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"strings"
	"time"
)

const (
	// BytesToGB conversion factor
	BytesToGB = 1024 * 1024 * 1024
	// BytesToMB conversion factor
	BytesToMB = 1024 * 1024
	// BytesToKB conversion factor
	BytesToKB = 1024
)

// GenerateID generates a random hex string of specified length
func GenerateID(length int) (string, error) {
	bytes := make([]byte, length/2)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// FormatTime formats time to RFC3339 string
func FormatTime(t time.Time) string {
	return t.Format(time.RFC3339)
}

// ParseTime parses RFC3339 time string
func ParseTime(timeStr string) (time.Time, error) {
	return time.Parse(time.RFC3339, timeStr)
}

// ToSnakeCase converts camelCase to snake_case
func ToSnakeCase(str string) string {
	var result strings.Builder
	for i, r := range str {
		if i > 0 && r >= 'A' && r <= 'Z' {
			result.WriteRune('_')
		}
		result.WriteRune(r)
	}
	return strings.ToLower(result.String())
}

// FormatBytes formats bytes to human readable format
func FormatBytes(bytes uint64) string {
	if bytes >= BytesToGB {
		return fmt.Sprintf("%.2f GB", float64(bytes)/BytesToGB)
	} else if bytes >= BytesToMB {
		return fmt.Sprintf("%.2f MB", float64(bytes)/BytesToMB)
	} else if bytes >= BytesToKB {
		return fmt.Sprintf("%.2f KB", float64(bytes)/BytesToKB)
	}
	return fmt.Sprintf("%d B", bytes)
}

// FormatDuration formats duration to human readable format
func FormatDuration(seconds uint64) string {
	duration := time.Duration(seconds) * time.Second
	days := duration / (24 * time.Hour)
	hours := (duration % (24 * time.Hour)) / time.Hour
	minutes := (duration % time.Hour) / time.Minute

	if days > 0 {
		return fmt.Sprintf("%dd %dh %dm", days, hours, minutes)
	} else if hours > 0 {
		return fmt.Sprintf("%dh %dm", hours, minutes)
	} else if minutes > 0 {
		return fmt.Sprintf("%dm", minutes)
	}
	return fmt.Sprintf("%.0fs", duration.Seconds())
}
