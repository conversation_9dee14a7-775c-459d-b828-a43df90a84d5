package sysinfo

import (
	"encoding/json"
	"fmt"
	"lil_box/pkg/commands"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/cobra"
)

var sarCmd = &cobra.Command{
	Use:   "sar [options] [interval] [count]",
	Short: "显示网络和TCP统计信息",
	Long:  "显示网络和TCP统计信息，类似于Linux的sar命令",
	Args:  cobra.MaximumNArgs(2),
	Run: func(cmd *cobra.Command, args []string) {
		jsonOutput, _ := cmd.Flags().GetBool("json")
		table, _ := cmd.Flags().GetBool("table")
		network, _ := cmd.Flags().GetString("network")
		
		interval := time.Second
		count := 1
		
		if len(args) >= 1 {
			if i, err := strconv.Atoi(args[0]); err == nil && i > 0 {
				interval = time.Duration(i) * time.Second
				count = 0 // Infinite by default when interval is specified
			}
		}
		
		if len(args) >= 2 {
			if c, err := strconv.Atoi(args[1]); err == nil && c > 0 {
				count = c
			}
		}
		
		// Parse network option
		showDev := false
		showTCP := false
		
		if network != "" {
			options := strings.Split(strings.ToUpper(network), ",")
			for _, opt := range options {
				switch strings.TrimSpace(opt) {
				case "DEV":
					showDev = true
				case "TCP", "ETCP":
					showTCP = true
				}
			}
		} else {
			// Default to DEV if no option specified
			showDev = true
		}
		
		if jsonOutput {
			result := make(map[string]interface{})
			
			if showDev {
				infos, err := commands.GetSarNetworkInfo(interval)
				if err != nil {
					fmt.Printf("Error getting network info: %v\n", err)
					return
				}
				result["network"] = infos
			}
			
			if showTCP {
				info, err := commands.GetSarTCPInfo()
				if err != nil {
					fmt.Printf("Error getting TCP info: %v\n", err)
					return
				}
				result["tcp"] = info
			}
			
			jsonData, err := json.MarshalIndent(result, "", "  ")
			if err != nil {
				fmt.Printf("Error formatting JSON: %v\n", err)
				return
			}
			fmt.Println(string(jsonData))
		} else if table {
			if showDev {
				if err := commands.PrintSarNetworkTable(); err != nil {
					fmt.Printf("Error: %v\n", err)
				}
			}
			// TCP table format not implemented yet
		} else {
			if showDev {
				if err := commands.PrintSarNetwork(interval, count); err != nil {
					fmt.Printf("Error: %v\n", err)
				}
			}
			
			if showTCP {
				if showDev {
					fmt.Println() // Add separator
				}
				if err := commands.PrintSarTCP(interval, count); err != nil {
					fmt.Printf("Error: %v\n", err)
				}
			}
		}
	},
}

func init() {
	sarCmd.Flags().StringP("network", "n", "DEV", "网络统计类型: DEV, TCP, ETCP")
	sarCmd.Flags().BoolP("table", "t", false, "以表格格式显示")
	rootCmd.AddCommand(sarCmd)
}
