package sysinfo

import (
	"encoding/json"
	"fmt"
	"lil_box/pkg/commands"
	"strconv"
	"time"

	"github.com/spf13/cobra"
)

var iostatCmd = &cobra.Command{
	Use:   "iostat [interval] [count]",
	Short: "显示I/O统计信息",
	Long:  "显示I/O统计信息，类似于Linux的iostat命令",
	Args:  cobra.MaximumNArgs(2),
	Run: func(cmd *cobra.Command, args []string) {
		jsonOutput, _ := cmd.Flags().GetBool("json")
		table, _ := cmd.Flags().GetBool("table")
		extended, _ := cmd.Flags().GetBool("extended")
		
		interval := time.Second
		count := 1
		
		if len(args) >= 1 {
			if i, err := strconv.Atoi(args[0]); err == nil && i > 0 {
				interval = time.Duration(i) * time.Second
				count = 0 // Infinite by default when interval is specified
			}
		}
		
		if len(args) >= 2 {
			if c, err := strconv.Atoi(args[1]); err == nil && c > 0 {
				count = c
			}
		}
		
		if jsonOutput {
			infos, err := commands.GetIostatInfo(interval)
			if err != nil {
				fmt.Printf("Error: %v\n", err)
				return
			}
			
			jsonData, err := json.MarshalIndent(infos, "", "  ")
			if err != nil {
				fmt.Printf("Error formatting JSON: %v\n", err)
				return
			}
			fmt.Println(string(jsonData))
		} else if table {
			if err := commands.PrintIostatTable(extended); err != nil {
				fmt.Printf("Error: %v\n", err)
			}
		} else {
			if err := commands.PrintIostat(interval, count, extended); err != nil {
				fmt.Printf("Error: %v\n", err)
			}
		}
	},
}

func init() {
	iostatCmd.Flags().BoolP("extended", "x", false, "显示扩展统计信息")
	iostatCmd.Flags().BoolP("table", "t", false, "以表格格式显示")
	rootCmd.AddCommand(iostatCmd)
}
