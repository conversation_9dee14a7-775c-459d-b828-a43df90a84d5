# Lil Box - 系统信息查询工具箱

一个简单易用的系统信息查询工具，使用 Go 语言开发，可以快速查询系统的内存、CPU、磁盘、网络等信息。

## ✨ 功能特性

- 🧠 **内存信息** - 查看内存使用情况、交换空间信息
- 🖥️ **CPU 信息** - 查看 CPU 型号、核心数、使用率、系统负载
- 💾 **磁盘信息** - 查看磁盘使用情况、I/O 统计
- 🌐 **网络信息** - 查看网络接口统计、连接信息
- 🖥️ **系统信息** - 查看主机名、操作系统、内核版本等
- 📊 **进程信息** - 查看 TOP 进程列表（按 CPU 使用率排序）
- 📄 **JSON 输出** - 支持 JSON 格式输出，便于脚本处理

## 🚀 快速开始

### 前置要求

- Go 1.21 或更高版本

### 安装

1. 克隆仓库
2. 进入项目目录
3. 安装依赖：
   ```bash
   go mod tidy
   ```

### 构建应用

```bash
# 构建当前平台
make build

# 或者直接使用go build
go build -o bin/lil_box
```

### 使用方法

```bash
# 查看所有系统信息
./bin/lil_box all

# 查看内存信息
./bin/lil_box memory

# 查看CPU信息
./bin/lil_box cpu

# 查看磁盘信息
./bin/lil_box disk

# 查看网络信息
./bin/lil_box network

# 查看系统信息
./bin/lil_box system

# 查看进程信息
./bin/lil_box process

# 以JSON格式输出
./bin/lil_box memory --json
```

## 📋 命令详情

### 基本命令

| 命令              | 别名              | 描述              |
| ----------------- | ----------------- | ----------------- |
| `lil_box all`     | `a`               | 显示所有系统信息  |
| `lil_box memory`  | `mem`, `m`        | 显示内存使用情况  |
| `lil_box cpu`     | `c`               | 显示 CPU 使用情况 |
| `lil_box disk`    | `d`               | 显示磁盘使用情况  |
| `lil_box network` | `net`, `n`        | 显示网络接口信息  |
| `lil_box system`  | `sys`, `s`        | 显示系统基本信息  |
| `lil_box process` | `proc`, `ps`, `p` | 显示进程信息      |

### 命令选项

| 选项                  | 描述                              |
| --------------------- | --------------------------------- |
| `--json`, `-j`        | 以 JSON 格式输出                  |
| `--verbose`, `-v`     | 详细输出                          |
| `--io`, `-i`          | (disk 命令) 显示磁盘 I/O 信息     |
| `--connections`, `-c` | (network 命令) 显示网络连接信息   |
| `--limit`, `-l`       | (process 命令) 限制显示的进程数量 |

### 使用示例

```bash
# 查看内存信息（简化别名）
./bin/lil_box m

# 查看磁盘I/O信息
./bin/lil_box disk --io

# 查看网络连接信息
./bin/lil_box network --connections

# 查看前20个进程
./bin/lil_box process --limit 20

# 以JSON格式查看所有信息
./bin/lil_box all --json
```

## 🛠️ 开发

### 测试

```bash
# 运行所有测试
make test

# 运行测试并生成覆盖率报告
make test-coverage
```

### 构建

```bash
# 构建当前平台
make build

# 交叉编译Linux版本
make build-linux

# 交叉编译Windows版本
make build-windows
```

### 快速演示

```bash
# 快速演示内存信息
make demo-memory

# 快速演示CPU信息
make demo-cpu

# 快速演示所有信息
make demo-all
```

## 📁 项目结构

```
.
├── cmd/
│   └── sysinfo/           # 命令行界面
├── pkg/
│   ├── sysinfo/           # 系统信息查询模块
│   └── utils/             # 工具函数
├── internal/              # 私有代码
├── configs/               # 配置文件
├── scripts/               # 构建脚本
├── docs/                  # 文档
├── bin/                   # 编译输出
├── main.go               # 应用入口
├── go.mod                # Go模块文件
├── Makefile              # 构建工具
└── README.md             # 项目说明
```

## 🤝 贡献

1. Fork 本仓库
2. 创建功能分支
3. 提交更改
4. 添加测试
5. 确保测试通过
6. 提交 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
