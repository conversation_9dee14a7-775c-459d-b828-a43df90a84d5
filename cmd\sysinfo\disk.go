package sysinfo

import (
	"encoding/json"
	"fmt"

	"lil_box/pkg/sysinfo"

	"github.com/spf13/cobra"
)

var diskCmd = &cobra.Command{
	Use:   "disk",
	Short: "查看磁盘使用情况",
	Long:  "显示磁盘使用情况，包括各个分区的容量、使用率和I/O统计信息",
	Aliases: []string{"d"},
	Run: func(cmd *cobra.Command, args []string) {
		jsonOutput, _ := cmd.Flags().GetBool("json")
		showIO, _ := cmd.Flags().GetBool("io")
		
		if jsonOutput {
			if showIO {
				info, err := sysinfo.GetDiskIOInfo()
				if err != nil {
					fmt.Printf("Error: %v\n", err)
					return
				}
				jsonData, err := json.MarshalIndent(info, "", "  ")
				if err != nil {
					fmt.Printf("Error formatting JSON: %v\n", err)
					return
				}
				fmt.Println(string(jsonData))
			} else {
				info, err := sysinfo.GetDiskInfo()
				if err != nil {
					fmt.Printf("Error: %v\n", err)
					return
				}
				jsonData, err := json.MarshalIndent(info, "", "  ")
				if err != nil {
					fmt.Printf("Error formatting JSON: %v\n", err)
					return
				}
				fmt.Println(string(jsonData))
			}
		} else {
			if showIO {
				if err := sysinfo.PrintDiskIOInfo(); err != nil {
					fmt.Printf("Error: %v\n", err)
				}
			} else {
				if err := sysinfo.PrintDiskInfo(); err != nil {
					fmt.Printf("Error: %v\n", err)
				}
			}
		}
	},
}

func init() {
	diskCmd.Flags().BoolP("io", "i", false, "显示磁盘I/O信息")
	rootCmd.AddCommand(diskCmd)
}
